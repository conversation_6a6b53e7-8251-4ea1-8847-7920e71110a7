<template>
  <div class="yibuyiping-detail-container">
    <!-- 返回按钮 -->
    <!-- <img class="cursor-pointer absolute top-[30px] left-[60px]" src="@/assets/images/xiangmu/back.png" alt=""
      @click="goBack" style="z-index: 1000" /> -->
      <div class="back-button" @click="goBack">
            <img src="@/assets/images/header/back.png" alt="" />
            <span>返回</span>
        </div>
        
        <!-- 顶部操作按钮 -->
        <div class="top-action-buttons">
          <div class="action-btn" @click="goToProjectDashboard">
            <span class="btn-text">项目看板</span>
          </div>
          <div class="action-btn" @click="goToSafetyPortal">
            <span class="btn-text">安全门户</span>
          </div>
          <div class="action-btn" @click="goToBackgroundManagement">
            <span class="btn-text">后台管理</span>
          </div>
        </div>
        
    <div class="main-content">
      <!-- 左侧统计面板 -->
      <div class="left-panel">
        <!-- 人员管理 -->
        <div class="stat-card">
          <div class="chart-header">
            <span>{{ $t('oneOfficeOneScreen.detail.leftPanel.personnelManagement') }}</span>
          </div>
          <div class="chart-body">
            <div class="renyuanTop">
              <div style="display: flex;align-items: center;">{{
                $t('oneOfficeOneScreen.detail.personnel.totalPersonnel') }}<span class="renyuanTop-num">28<span
                    class="unit">{{ $t('oneOfficeOneScreen.detail.units.people') }}</span></span></div>
            </div>
            <div class="chart" style="height: 140px; margin: 5px 0;">
              <PersonnelRingChart :data="personnelData" height="140px" />
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="chart-header">
            <span>{{ $t('oneOfficeOneScreen.detail.leftPanel.projectInspection') }}</span>
          </div>
          <div class="chart-body">
            <div class="renyuanTop">
              <div style="display: flex;align-items: center;">{{
                $t('oneOfficeOneScreen.detail.inspection.totalProjects') }}<span class="renyuanTop-num">28<span
                    class="unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span></span></div>
            </div>
            <div class="numOfTime-container">
              <div class="numOfTime"></div>
            </div>
            <div class="inspection-boxes">
              <div class="inspection-box">
                <div class="box-left">{{ $t('oneOfficeOneScreen.detail.inspection.group') }}</div>
                <div class="box-right">
                  <span class="num">4</span>
                  <span class="unit">{{ $t('oneOfficeOneScreen.detail.inspection.times') }}</span>
                </div>
              </div>
              <div class="inspection-box">
                <div class="box-left">{{ $t('oneOfficeOneScreen.detail.inspection.subsidiary') }}</div>
                <div class="box-right">
                  <span class="num">8</span>
                  <span class="unit">{{ $t('oneOfficeOneScreen.detail.inspection.times') }}</span>
                </div>
              </div>
              <div class="inspection-box">
                <div class="box-left">{{ $t('oneOfficeOneScreen.detail.inspection.project') }}</div>
                <div class="box-right">
                  <span class="num">12</span>
                  <span class="unit">{{ $t('oneOfficeOneScreen.detail.inspection.times') }}</span>
                </div>
              </div>
              <div class="inspection-box">
                <div class="box-left">{{ $t('oneOfficeOneScreen.detail.inspection.selfInspection') }}</div>
                <div class="box-right">
                  <span class="num">16</span>
                  <span class="unit">{{ $t('oneOfficeOneScreen.detail.inspection.times') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="chart-header">
            <span>{{ $t('oneOfficeOneScreen.detail.leftPanel.safetyEducation') }}</span>
          </div>
          <div class="chart-body">
            <div class="safety-education-container">
              <div class="safety-education-item">
                <div class="safety-content">
                  <div class="safety-label">{{ $t('oneOfficeOneScreen.detail.safetyEducation.monthlyExamPassRate') }}
                  </div>
                  <div class="safety-value"><span class="safety-number">78.12</span><span class="safety-unit">{{
                    $t('oneOfficeOneScreen.detail.units.percent') }}</span>
                  </div>
                </div>
              </div>
              <div class="safety-education-item">
                <div class="safety-content">
                  <div class="safety-label">{{ $t('oneOfficeOneScreen.detail.safetyEducation.safetyTrainingCoverage') }}
                  </div>
                  <div class="safety-value"><span class="safety-number">95.36</span><span class="safety-unit">{{
                    $t('oneOfficeOneScreen.detail.units.percent') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="chart-header">
            <span>{{ $t('oneOfficeOneScreen.detail.leftPanel.hazardOverview') }}</span>
          </div>
          <div class="chart-body">
            <div class="position-box">
            </div>
            <div class="dangerTotal">12</div>
            <div class="chart3d"></div>
          </div>
        </div>

      </div>

      <!-- 中央地图区域 -->
      <div class="center-map">
        <div class="map-container">
          <!-- 统计指标顶部 -->
          <div class="top-stats">
            <div class="stat-box">
              <div class="icon purple"><img src="@/assets/images/yibuyiping/图标.png" alt=""></div>
              <div class="content">
                <div class="label">{{ $t('oneOfficeOneScreen.detail.centerStats.greenConstructionAlerts') }}</div>
                <div class="number">126<span class="unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span></div>
              </div>
            </div>
            <div class="stat-box">
              <div class="icon teal"><img src="@/assets/images/yibuyiping/图标1.png" alt=""></div>
              <div class="content">
                <div class="label">{{ $t('oneOfficeOneScreen.detail.centerStats.aiWarnings') }}</div>
                <div class="number">67<span class="unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span></div>
              </div>
            </div>
            <div class="stat-box">
              <div class="icon blue"><img src="@/assets/images/yibuyiping/图标2.png" alt=""></div>
              <div class="content">
                <div class="label">{{ $t('oneOfficeOneScreen.detail.centerStats.videoMonitoringDevices') }}</div>
                <div class="number">78<span class="unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span></div>
              </div>
            </div>
            <div class="stat-box">
              <div class="icon blue"><img src="@/assets/images/yibuyiping/danger.png" alt=""></div>
              <div class="content">
                <div class="label">{{ $t('oneOfficeOneScreen.detail.centerStats.majorHazardousProjects') }}</div>
                <div class="number">12<span class="unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span></div>
              </div>
            </div>
          </div>

          <!-- 底部按钮 -->
          <div class="bottom-buttons">
            <div class="action-button safety" @click="goToSafety">
              <img src="@/assets/images/yibuyiping/secure.png" alt="安全">
            </div>
            <div class="action-button quality" @click="goToQuality">
              <img src="@/assets/images/yibuyiping/quality.png" alt="质量">
            </div>
            <div class="action-button environment" @click="goToEnvironment">
              <img src="@/assets/images/yibuyiping/environment.png" alt="环境">
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 质量管理 -->
        <div class="stat-card">
          <div class="chart-header">
            <span>{{ $t('oneOfficeOneScreen.detail.qualityManagement.title') }}</span>
          </div>
          <div class="chart-body">
            <div class="quality-stats">
              <!-- 第一行rectangle.png背景 -->
              <div class="rectangle-container">
                <div class="rectangle-bg">
                  <div class="stats-info">
                    <div class="text-info">
                      <div class="title">{{ $t('oneOfficeOneScreen.detail.qualityManagement.totalInspections') }}</div>
                      <div class="number">18<span class="unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第二行两个wide.png左右并列 -->
              <div class="wide-container">
                <div class="wide-item">
                  <div class="circle-progress" id="acceptanceChart">
                    <!-- ECharts环形图将在这里渲染 -->
                  </div>
                  <div class="label">{{ $t('oneOfficeOneScreen.detail.qualityManagement.firstTimeAcceptanceRate') }}
                  </div>
                </div>
                <div class="wide-item">
                  <div class="circle-progress" id="repairChart">
                    <!-- ECharts环形图将在这里渲染 -->
                  </div>
                  <div class="label">{{ $t('oneOfficeOneScreen.detail.qualityManagement.repairRectificationRate') }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 质量问题趋势 -->
        <div class="stat-card">
          <div class="chart-header">
            <span>{{ $t('oneOfficeOneScreen.detail.qualityTrend.title') }}</span>
          </div>
          <div class="chart-body">
            <!-- 第一排：环形图 -->
            <div class="trend-chart-container">
              <div class="legend-left" style="padding-left: 10px;">
                <div class="legend-item">
                  <div class="legend-dot overdue"></div>
                  <span>{{ $t('oneOfficeOneScreen.detail.qualityTrend.overdue') }}：2{{
                    $t('oneOfficeOneScreen.detail.units.items') }}</span>
                </div>
              </div>
              <div class="trend-chart" id="trendChart">
                <!-- 环形图将在这里渲染 -->
              </div>
              <div class="legend-right" style="margin-right: 10px;">
                <div class="legend-item">
                  <div class="legend-dot normal"></div>
                  <span>{{ $t('oneOfficeOneScreen.detail.qualityTrend.notOverdue') }}：2{{
                    $t('oneOfficeOneScreen.detail.units.items') }}</span>
                </div>
              </div>
            </div>

            <!-- 第二排：状态项 -->
            <div class="trend-status-container">
              <div class="status-item urgent">
                <div class="status-icon">
                  <img src="@/assets/images/yibuyiping/danger.png" alt="紧急">
                </div>
                <div class="status-content">
                  <span class="status-label">{{ $t('oneOfficeOneScreen.detail.qualityTrend.veryUrgent') }}</span>
                  <span class="status-number">2</span>
                </div>
              </div>
              <div class="status-item normal">
                <div class="status-icon">
                  <!-- 使用 SVG 图标替代缺失的 normal.png -->
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" stroke="#00C8FF" stroke-width="2" fill="rgba(0, 200, 255, 0.2)" />
                    <path d="M9 12l2 2 4-4" stroke="#00C8FF" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </div>
                <div class="status-content">
                  <span class="status-label">{{ $t('oneOfficeOneScreen.detail.qualityTrend.generalSituation') }}</span>
                  <span class="status-number">4</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="chart-header">
            <span>{{ $t('oneOfficeOneScreen.detail.awards.title') }}</span>
          </div>
          <div class="chart-body">
            <div class="awards-scroll-container" @mouseenter="pauseScrolling" @mouseleave="resumeScrolling">
              <!-- 顶部渐变遮罩 -->
              <div class="scroll-mask scroll-mask-top"></div>
              <!-- 底部渐变遮罩 -->
              <div class="scroll-mask scroll-mask-bottom"></div>

              <div class="awards-list" ref="awardsListRef" :class="{ paused: isScrollPaused }">
                <!-- 第一组数据 -->
                <div class="award-item" v-for="(award, index) in awardsList" :key="`award-first-${index}`"
                  :class="`award-level-${award.level}`">
                  <div class="award-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
                        :fill="getAwardColor(award.level)" />
                    </svg>
                  </div>
                  <div class="award-name">{{ award.name }}</div>
                  <div class="award-count">
                    <span class="count-number">{{ award.count }}</span>
                    <span class="count-unit">个</span>
                  </div>
                </div>
                <!-- 第二组数据（无缝循环） -->
                <div class="award-item" v-for="(award, index) in awardsList" :key="`award-second-${index}`"
                  :class="`award-level-${award.level}`">
                  <div class="award-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
                        :fill="getAwardColor(award.level)" />
                    </svg>
                  </div>
                  <div class="award-name">{{ award.name }}</div>
                  <div class="award-count">
                    <span class="count-number">{{ award.count }}</span>
                    <span class="count-unit">个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 法律法规 -->
        <div class="stat-card">
          <div class="chart-header">
            <span>{{ $t('oneOfficeOneScreen.detail.legalRegulations.title') }}</span>
          </div>
          <div class="chart-body">
            <div class="legal-regulations-container">
              <div class="legal-item">
                <div class="legal-label">{{ $t('oneOfficeOneScreen.detail.legalRegulations.regulations') }}：</div>
                <div class="legal-count">
                  <span class="legal-number">24</span>
                  <span class="legal-unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span>
                </div>
              </div>
              <div class="legal-item">
                <div class="legal-label">{{ $t('oneOfficeOneScreen.detail.legalRegulations.standards') }}：</div>
                <div class="legal-count">
                  <span class="legal-number">18</span>
                  <span class="legal-unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span>
                </div>
              </div>
              <div class="legal-item">
                <div class="legal-label">{{ $t('oneOfficeOneScreen.detail.legalRegulations.notices') }}：</div>
                <div class="legal-count">
                  <span class="legal-number">32</span>
                  <span class="legal-unit">{{ $t('oneOfficeOneScreen.detail.units.items') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import PersonnelRingChart from '@/components/PersonnelRingChart.vue';
import * as echarts from 'echarts';
import 'echarts-gl';
import overviewBg from '@/assets/images/yibuyiping/overviewOfHiddenDangers.png';
import { checkWebGLSupport, checkDevicePerformance, create2DPieChart } from '@/utils/webglUtils.js';

const router = useRouter();
const { t } = useI18n();

const goBack = () => {
  router.push('/yibuyiping/index');
};

// 跳转到安全页面
const goToSafety = () => {
  router.push('/yibuyiping/anquan'); // 根据实际路由路径调整
};

// 跳转到质量页面
const goToQuality = () => {
  router.push('/yibuyiping/zhiliang'); // 根据实际路由路径调整
};

// 跳转到环境页面
const goToEnvironment = () => {
  router.push('/yibuyiping/huanjing'); // 根据实际路由路径调整
};

// 人员管理数据
const personnelData = ref([
  { name: t('oneOfficeOneScreen.detail.personnel.constructionWorkers'), value: 17, itemStyle: { color: '#0085FF' } },
  { name: t('oneOfficeOneScreen.detail.personnel.safetyManagers'), value: 8, itemStyle: { color: '#87DBFF' } },
  { name: t('oneOfficeOneScreen.detail.personnel.managementStaff'), value: 3, itemStyle: { color: '#EEC01B' } }
]);

// 限制时数数据
const timeData = ref([
  { name: t('oneOfficeOneScreen.detail.hazard.rectified'), value: 9, itemStyle: { color: '#8979FF' } },
  { name: t('oneOfficeOneScreen.detail.hazard.unrectified'), value: 3, itemStyle: { color: '#FF928A' } }
]);

// 奖项数据
const awardsList = ref([
  { name: t('oneOfficeOneScreen.detail.awards.nationalQualityAward'), count: 2, level: 'national' },
  { name: t('oneOfficeOneScreen.detail.awards.provincialSafetyAward'), count: 5, level: 'provincial' },
  { name: t('oneOfficeOneScreen.detail.awards.municipalGreenBuildingAward'), count: 3, level: 'municipal' },
  { name: t('oneOfficeOneScreen.detail.awards.industryQualityAward'), count: 1, level: 'industry' },
  { name: t('oneOfficeOneScreen.detail.awards.technicalInnovationAward'), count: 4, level: 'innovation' },
  { name: t('oneOfficeOneScreen.detail.awards.environmentalProtectionAward'), count: 2, level: 'environmental' },
  { name: t('oneOfficeOneScreen.detail.awards.smartConstructionAward'), count: 1, level: 'smart' },
  { name: t('oneOfficeOneScreen.detail.awards.lubanAward'), count: 1, level: 'luban' }
]);

// 滚动控制
const isScrollPaused = ref(false);
const awardsListRef = ref(null);
let scrollObserver = null;

// 暂停滚动
const pauseScrolling = () => {
  isScrollPaused.value = true;
};

// 恢复滚动
const resumeScrolling = () => {
  isScrollPaused.value = false;
};

// 根据奖项级别获取颜色
const getAwardColor = (level) => {
  const colors = {
    national: '#FFD700',    // 金色 - 国家级
    luban: '#FFD700',       // 金色 - 鲁班奖
    provincial: '#C0C0C0',  // 银色 - 省级
    municipal: '#CD7F32',   // 铜色 - 市级
    industry: '#00C8FF',    // 蓝色 - 行业
    innovation: '#9B59B6',  // 紫色 - 创新
    environmental: '#27AE60', // 绿色 - 环保
    smart: '#E67E22'        // 橙色 - 智能
  };
  return colors[level] || '#FFD700';
};

// 智能滚动控制：当页面不可见时暂停动画
const setupScrollOptimization = () => {
  // 监听页面可见性变化
  const handleVisibilityChange = () => {
    if (document.hidden) {
      isScrollPaused.value = true;
    } else {
      isScrollPaused.value = false;
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);

  // 使用 Intersection Observer 监听容器是否在视口中
  if (awardsListRef.value) {
    scrollObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (!entry.isIntersecting && !document.hidden) {
            isScrollPaused.value = true;
          } else if (entry.isIntersecting && !document.hidden) {
            isScrollPaused.value = false;
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    scrollObserver.observe(awardsListRef.value.parentElement);
  }

  // 清理函数
  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    if (scrollObserver) {
      scrollObserver.disconnect();
    }
  };
};

// 获取参数方程
function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2;
  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;

  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3;

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20
    },
    x: function (u, v) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u, v) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * height;
      }
      // 创建柱状图效果：顶部平面在height高度，底部平面在0高度
      return Math.sin(v) > 0 ? height : 0;
    }
  };
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData, internalDiameterRatio) {
  let series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  let legendData = [];
  let k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3;

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;

    let seriesItem = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k
      },
      emphasis: {
        itemStyle: {
          opacity: 1
        }
      }
    };

    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle = {};
      typeof pieData[i].itemStyle.color != 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null;
      typeof pieData[i].itemStyle.opacity != 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null;
      seriesItem.itemStyle = itemStyle;
    }
    series.push(seriesItem);
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      0.8 // 增加高度，让3D柱状图效果更明显
    );
    startValue = endValue;
    legendData.push(series[i].name);
  }

  // 不再添加底座

  return series;
}

// 使用工具函数替换原有的检测逻辑

// 初始化3D环形图
const initChart3D = () => {
  const chartDom = document.querySelector('.chart3d');
  if (!chartDom) return;

  // 检查WebGL支持和设备性能
  if (!checkWebGLSupport() || !checkDevicePerformance()) {
    console.warn('WebGL不支持或设备性能较低，将使用2D替代方案');
    initChart2DFallback();
    return;
  }

  let myChart;
  try {
    myChart = echarts.init(chartDom);
  } catch (error) {
    console.error('ECharts初始化失败:', error);
    initChart2DFallback();
    return;
  }

  // 准备饼图数据
  const optionsData = [
    {
      name: t('oneOfficeOneScreen.detail.hazard.rectified'),
      value: 9,
      itemStyle: {
        color: '#8979FF',
        opacity: 1
      }
    },
    {
      name: t('oneOfficeOneScreen.detail.hazard.unrectified'),
      value: 3,
      itemStyle: {
        color: '#FF928A',
        opacity: 1
      }
    }
  ];

  // 获取3D饼图系列
  const series = getPie3D(optionsData, 0.65);

  // 图表配置
  const option = {
    backgroundColor: {
      image: overviewBg, // 这里用的是 import 进来的图片路径
      repeat: 'no-repeat',
      x: 'center',
      y: 'center',
      width: '40%',
      height: '40%',
    },
    legend: {
      tooltip: {
        show: false,
      },
      orient: 'vertical',
      right: '5%',
      top: 'middle',
      itemGap: 20,
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 12,
      formatter: function (name) {
        // 找到对应的数据项
        let value = 0;
        for (let i = 0; i < optionsData.length; i++) {
          if (optionsData[i].name === name) {
            value = optionsData[i].value;
            break;
          }
        }
        return name + '   ' + value + t('oneOfficeOneScreen.detail.units.items');
      },
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
    },
    tooltip: {
      formatter: params => {
        if (params.seriesName !== 'mouseoutSeries') {
          return `${params.seriesName}: ${params.seriesIndex >= 0 ? series[params.seriesIndex].pieData.value : ''}${t('oneOfficeOneScreen.detail.units.items')}`;
        }
      },
      textStyle: {
        fontSize: 14
      },
    },
    xAxis3D: {
      min: -1.2,
      max: 1.2,
    },
    yAxis3D: {
      min: -1.2,
      max: 1.2,
    },
    zAxis3D: {
      min: -0.5,
      max: 1.6,
    },
    grid3D: {
      show: false,
      boxWidth: 260,
      boxHeight: 60,
      top: '-6%',
      bottom: '15%',
      left: '-10%',
      viewControl: {
        distance: 190,
        alpha: 35,
        beta: 0,
        autoRotate: false,
        rotateSensitivity: 0,
        autoRotateSpeed: 8,
        autoRotateDirection: 1,
      },
      // 简化后处理效果以提高兼容性
      postEffect: {
        enable: false // 禁用复杂的后处理效果
      },
      temporalSuperSampling: {
        enable: false // 禁用时域超采样
      },
      environment: 'none'
    },
    graphic: {
      elements: [
        {
          type: 'text',
          left: '31%',
          bottom: '12%',
          z: 100,
          style: {
            text: t('oneOfficeOneScreen.detail.hazard.totalHazards'),
            fontSize: 18,
            fontWeight: 'normal',
            fill: 'rgba(255, 255, 255)',
            textAlign: 'center',
            textShadow: '0 1px 3px rgba(0, 0, 0, 0.3)'
          }
        }
      ],
    },
    series: series
  };

  try {
    myChart.setOption(option);
  } catch (error) {
    console.error('3D图表渲染失败:', error);
    // 销毁当前图表实例，使用2D替代
    myChart.dispose();
    initChart2DFallback();
    return;
  }

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 初始化蓝色环形图
const initBlueRingChart = (chartId, seriesData, total, title, position = { left: '50%', top: '10%', centerX: '50%', centerY: '60%' }) => {
  const chartDom = document.getElementById(chartId);
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      show: false
    },
    title: {
      show: true,
      text: '{number|' + seriesData + '}{unit|%}',
      textStyle: {
        fontSize: 14,
        lineHeight: 26,
        color: '#0085FF',
        rich: {
          number: {
            fontSize: 28,
            fontWeight: 700,
            fontFamily: 'TCloudNumber'
          },
          unit: {
            fontSize: 14,
            fontWeight: 400,
            fontFamily: 'Microsoft YaHei'
          }
        }
      },
      textAlign: 'center',
      left: position.left,
      top: position.top
    },
    series: [
      {
        name: 'Progress',
        type: 'pie',
        radius: ['70%', '85%'],
        center: [position.centerX || '50%', position.centerY || '60%'],
        startAngle: -127,
        data: [
          {
            value: seriesData,
            name: '',
            itemStyle: {
              color: '#0085FF',
              borderRadius: 10,
              borderWidth: 0
            }
          },
          {
            value: (total - seriesData),
            name: '',
            emphasis: {
              disabled: true,
              label: {
                show: false
              }
            },
            itemStyle: {
              color: 'rgba(0, 224, 255, .15)',
              borderRadius: 10,
              borderWidth: 0
            },
            tooltip: {
              show: false
            }
          },
          {
            value: (total / 0.8) * 0.2,
            itemStyle: {
              color: 'transparent',
              decal: {
                symbol: 'none'
              }
            },
            label: {
              show: false
            },
            emphasis: {
              disabled: true
            },
            tooltip: {
              show: false
            }
          }
        ],
        emphasis: {
          disabled: true
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  };

  myChart.setOption(option);

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 初始化质量问题趋势环形图
const initTrendChart = () => {
  const chartDom = document.getElementById('trendChart');
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 计算总数
  const data = [
    {
      value: 2,
      name: t('oneOfficeOneScreen.detail.qualityTrend.overdue'),
      itemStyle: {
        color: '#FF8F39'
      }
    },
    {
      value: 2,
      name: t('oneOfficeOneScreen.detail.qualityTrend.notOverdue'),
      itemStyle: {
        color: '#2EB9FF'
      }
    }
  ];

  const total = data.reduce((sum, item) => sum + item.value, 0);

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    title: {
      text: `{number|${total}}{unit|${t('oneOfficeOneScreen.detail.units.items')}}\n{label|${t('common.total')}}`,
      left: '52%',
      top: '50%',
      textAlign: 'center',
      textVerticalAlign: 'middle',
      textStyle: {
        rich: {
          number: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#FFFFFF',
            fontFamily: 'TCloudNumber',
            lineHeight: 28
          },
          unit: {
            fontSize: 16,
            color: '#FFFFFF',
            fontFamily: 'Microsoft YaHei',
            lineHeight: 28
          },
          label: {
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
            fontFamily: 'Microsoft YaHei',
            lineHeight: 20
          }
        }
      }
    },
    series: [
      {
        name: t('oneOfficeOneScreen.detail.qualityTrend.title'),
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['56%', '50%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  };

  myChart.setOption(option);

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 2D降级方案
const initChart2DFallback = () => {
  const chartDom = document.querySelector('.chart3d');
  if (!chartDom) return;

  const data = [
    {
      value: 9,
      name: t('oneOfficeOneScreen.detail.hazard.rectified'),
      itemStyle: {
        color: '#8979FF'
      }
    },
    {
      value: 3,
      name: t('oneOfficeOneScreen.detail.hazard.unrectified'),
      itemStyle: {
        color: '#FF928A'
      }
    }
  ];

  const myChart = create2DPieChart(chartDom, data, {
    title: `${t('oneOfficeOneScreen.detail.hazard.totalHazards')}\n12${t('oneOfficeOneScreen.detail.units.items')}`,
    seriesName: t('oneOfficeOneScreen.detail.hazard.totalHazards'),
    titleColor: '#FFFFFF',
    titleFontSize: 16
  });

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

let cleanupScrollOptimization = null;

onMounted(() => {
  // 初始化3D环形图
  initChart3D();

  // 初始化质量管理的两个蓝色环形图
  initBlueRingChart('acceptanceChart', 12, 100, '一次性验收通过率', {
    left: '45%',
    top: '30%',
    centerX: '50%',
    centerY: '50%'
  });
  initBlueRingChart('repairChart', 78, 100, '返修整改率', {
    left: '45%',
    top: '30%',
    centerX: '50%',
    centerY: '50%'
  });

  // 初始化质量问题趋势环形图
  initTrendChart();

  // 设置滚动优化
  setTimeout(() => {
    cleanupScrollOptimization = setupScrollOptimization();
  }, 100);
});

// 组件卸载时清理
onUnmounted(() => {
  if (cleanupScrollOptimization) {
    cleanupScrollOptimization();
  }
});
</script>

<style scoped lang="scss">
.yibuyiping-detail-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  //background: linear-gradient(135deg, #0a1628 0%, #1a2744 100%);
  background: url('@/assets/images/yibuyiping/center.png') no-repeat center/cover;
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
  pointer-events: all;
}
.back-button {
    position: absolute;
    top: 30px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
    z-index: 1000;
}
// 添加3D图表的样式
.chart3d {
  width: 100%;
  height: 280px;
  position: relative;
}

.position-box {
  background: url('@/assets/images/yibuyiping/overviewOfHiddenDangers.png') no-repeat center/cover;
  background-size: 100% 100%;
  width: 70%;
  height: 79%;
  position: absolute;
  top: 10%;
  left: 6%;
  right: 0;
  bottom: 0;
}

.header-nav {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(0, 20, 40, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  z-index: 100;
  border-bottom: 1px solid rgba(0, 200, 255, 0.3);

  .nav-item {
    color: #ffffff;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;

    &:hover {
      background: rgba(0, 200, 255, 0.2);
    }

    &.active {
      background: rgba(0, 200, 255, 0.3);
      color: #00c8ff;
    }

    .back-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }

  .nav-group {
    display: flex;
    gap: 20px;

    &.right {
      gap: 30px;
    }
  }

  .title-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: #00c8ff;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 200, 255, 0.5);
  }
}

.main-content {
  margin-top: 120px;
  display: flex;
  gap: 20px;
  padding: 0 20px 20px 20px;
  height: calc(100% - 120px);
}

.left-panel,
.right-panel {
  width: 21%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
}

.center-map {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-card {
  background: rgba(5, 30, 60, 0.8);
  border: 1px solid rgba(0, 200, 255, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  flex: 1;
  min-height: 200px;

  .chart-header {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding-left: 50px;
    background: url('@/assets/images/zichan/box-title.png') no-repeat center/cover;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;

    span {
      font-family: "Alimama", sans-serif;
      font-weight: 700;
      font-size: 16px;
      background-image: linear-gradient(to bottom,
          #006FD0 0%,
          #FFFFFF 50%,
          #006FD0 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }
  }

  .chart-body {
    width: 100%;
    height: calc(100% - 40px);
    padding: 15px 15px 10px 15px;
    position: relative;
    background: url('@/assets/images/zichan/box-content.png') no-repeat center/cover;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .numOfTime {
      width: 18%;
      height: 23px;
      background: url('@/assets/images/yibuyiping/cishu.png') no-repeat center/cover;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }

  .chart-title {
    font-size: 14px;
    background: url('@/assets/images/yibuyiping/renyuanguanli-bottom.png') no-repeat center/cover;
    width: 100%;
    height: 60px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: left;
    padding-left: 60px;
    margin-top: 5px;
  }

  .renyuanTop {
    font-size: 14px;
    background: url('@/assets/images/yibuyiping/renyuanguanli-top.png') no-repeat center/cover;
    width: 100%;
    height: 43px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: left;
    padding-left: 100px;
    font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
    font-weight: bold;
    font-size: 18px;
    color: #FFFFFF;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .renyuanTop-num {
    font-family: TCloudNumber, TCloudNumber;
    font-weight: bold;
    font-size: 28px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 100px;
    display: block;

    .unit {
      font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
      font-weight: normal;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.5);
      line-height: 24px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }

  .chart-title-r {
    font-size: 12px;
    background: url('@/assets/images/yibuyiping/renyuanguanli-bottom.png') no-repeat center/cover;
    width: 100%;
    height: 43px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: left;
    padding-left: 60px;
  }

  .chart-title-num {
    font-family: TCloudNumber, TCloudNumber;
    font-weight: bold;
    font-size: 28px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: block;
  }

  .card-title {
    color: #00c8ff;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    .icon {
      margin-right: 8px;
      color: #00c8ff;
    }
  }

  .big-number {
    font-size: 32px; // PC端字体大小调整
    color: #ffffff;
    font-weight: bold;
    text-align: center;
    margin: 15px 0; // PC端间距调整
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

.progress-ring {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .ring-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;

    .color-red,
    .color-orange,
    .color-blue,
    .color-cyan,
    .color-yellow {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

    .color-red {
      background: #ff4757;
    }

    .color-orange {
      background: #ffa502;
    }

    .color-blue {
      background: #3742fa;
    }

    .color-cyan {
      background: #00d2d3;
    }

    .color-yellow {
      background: #ffdd59;
    }
  }
}

.project-stats {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/yibuyiping/projectInspect.png') no-repeat center/cover;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
}

.personnel-info {
  text-align: center;

  .total-count {
    font-size: 24px; // PC端字体大小调整
    color: #00c8ff;
    font-weight: bold;
    margin-bottom: 15px; // PC端间距调整

    .unit {
      font-size: 14px; // PC端字体大小调整
      margin-left: 4px;
    }
  }

  .personnel-breakdown {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;

    .breakdown-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #ffffff;
      justify-content: flex-start;
    }
  }

  .safety-training {
    color: #ffffff;
    font-size: 12px;
    background: rgba(0, 200, 255, 0.1);
    padding: 8px;
    border-radius: 4px;
  }
}

.map-container {
  padding: 20px;
  height: 100%;
  // backdrop-filter: blur(10px);
  position: relative;
  display: flex;
  flex-direction: column;
}

.top-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30px;

  .stat-box {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #ffffff;
    padding: 10px 15px;
    border-radius: 8px;
    background: linear-gradient(270deg, rgba(30, 79, 124, 0.7) 0%, rgba(0, 133, 255, 0) 100%);

    .icon {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      flex-shrink: 0;

      &.purple {
        background: rgba(138, 43, 226, 0.3);
      }

      &.teal {
        background: rgba(0, 210, 211, 0.3);
      }

      &.blue {
        background: rgba(55, 66, 250, 0.3);
      }
    }

    .content {
      text-align: left;
      margin-left: 10px;

      .label {
        font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
        font-weight: normal;
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .number {
        font-size: 28px;
        font-weight: bold;
        color: #fff;

        .unit {
          font-size: 14px;
          margin-left: 4px;
        }
      }
    }
  }
}

.world-map {
  flex: 1;
  position: relative;
  min-height: 400px;

  .map-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #0a1628 0%, #1a2744 100%);
    border-radius: 8px;
    overflow: hidden;
  }

  .continent {
    position: absolute;
    background: rgba(0, 150, 200, 0.3);
    border: 1px solid rgba(0, 200, 255, 0.5);
    border-radius: 20px;
    box-shadow: 0 0 15px rgba(0, 200, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 200, 255, 0.4);
      box-shadow: 0 0 25px rgba(0, 200, 255, 0.4);
    }

    &.asia {
      border-radius: 30px 10px 25px 15px;
      background: rgba(0, 180, 150, 0.3);
    }

    &.africa {
      border-radius: 15px 25px 20px 10px;
      background: rgba(200, 150, 0, 0.3);
    }

    &.europe {
      border-radius: 20px 15px 10px 20px;
      background: rgba(150, 0, 200, 0.3);
    }

    &.north-america {
      border-radius: 25px 20px 30px 15px;
      background: rgba(0, 200, 100, 0.3);
    }

    &.south-america {
      border-radius: 10px 15px 25px 20px;
      background: rgba(200, 100, 0, 0.3);
    }

    &.australia {
      border-radius: 50%;
      background: rgba(200, 0, 150, 0.3);
    }
  }

  .map-point {
    position: absolute;
    z-index: 20;

    .point-marker {
      background: rgba(0, 200, 255, 0.8);
      color: #ffffff;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      animation: pulse 2s infinite;

      &::before {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid rgba(0, 200, 255, 0.8);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: 6px;
        background: #00c8ff;
        border-radius: 50%;
        box-shadow: 0 0 10px rgba(0, 200, 255, 0.8);
        animation: glow 1.5s ease-in-out infinite alternate;
      }
    }
  }
}

.bottom-buttons {
  display: flex;
  justify-content: center;
  gap: 50px;
  margin-top: 20px;
  margin-bottom: 20px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 2%;

  .action-button {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;

    img {
      width: 100%;
      height: 100%;
      margin-bottom: 5px;
    }

    span {
      margin-top: 5px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
    }
  }
}

.quality-stats {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .rectangle-container {
    width: 93%;
    height: 56px;

    .rectangle-bg {
      width: 100%;
      height: 100%;
      background: url('@/assets/images/yibuyiping/rectangle.png') no-repeat center/cover;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      padding: 0 20px;

      .stats-info {
        width: 100%;
        display: flex;
        align-items: center;

        .icon-box {
          width: 35px;
          height: 35px;
          margin-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 25px;
            height: 25px;
          }
        }

        .text-info {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 100px;

          .title {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }

          .number {
            font-family: TCloudNumber, TCloudNumber;
            font-weight: bold;
            font-size: 20px;
            color: #3C9EFF;

            .unit {
              font-size: 14px;
              font-family: "Microsoft YaHei", sans-serif;
              margin-left: 2px;
            }
          }
        }
      }
    }
  }

  .wide-container {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    .wide-item {
      width: 43%;
      height: 171px;
      background: url('@/assets/images/yibuyiping/wide.png') no-repeat center/cover;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .circle-progress {
        width: 90px;
        height: 90px;
        background: transparent;
      }
    }

    .label {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      position: absolute;
      top: 84%;
    }
  }
}

.chart {
  width: 100%;
  height: 100%;
}

.trend-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  .total-circle {
    width: 120px;
    height: 120px;
    background: url('@/assets/images/yibuyiping/circle.png') no-repeat center/contain;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 10px;

    .total-number {
      font-family: TCloudNumber, TCloudNumber;
      font-weight: bold;
      font-size: 28px; // PC端字体大小调整
      color: #FFFFFF;
      line-height: 1;
    }

    .total-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .status-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 15px;
  }

  .trend-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 20px;

    .period {
      font-size: 14px;
      color: #ffffff;
      margin-bottom: 5px;
    }

    &:first-child .count {
      color: #FF9500;
    }

    &:last-child .count {
      color: #00c8ff;
    }

    .count {
      font-weight: bold;
      font-size: 20px;

      .unit {
        font-size: 14px;
        margin-left: 4px;
      }
    }
  }

  .progress-bar {
    display: flex;
    height: 34px;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 10px;
    width: 100%;

    .bar-section {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-size: 14px;

      &.red {
        background: #ff4757;
      }

      &.blue {
        background: #3742fa;
      }
    }
  }
}

.legal-stats {
  background: url('@/assets/images/yibuyiping/law.png') no-repeat center/cover;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  position: relative;

  .legal-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 0;
  }

  .legal-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;

    &:nth-child(1) {
      /* 左上角 */
      top: -6px;
      left: -30px;
    }

    &:nth-child(2) {
      /* 右上角 */
      top: -6px;
      right: -39px;
    }

    &:nth-child(3) {
      /* 左下角 */
      bottom: -15px;
      left: -31px;
    }

    &:nth-child(4) {
      /* 右下角 */
      bottom: -14px;
      right: -41px;
    }

    .number {
      font-family: TCloudNumber, TCloudNumber;
      font-weight: bold;
      font-size: 20px; // PC端字体大小调整
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(89.99999999999973deg, #E5F5FF 0%, #76B6FF 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 30px; // PC端间距调整

      .unit {
        font-size: 14px; // PC端字体大小调整
        margin-left: 4px;
      }
    }

    .label {
      color: #ffffff;
      font-size: 14px;
      font-weight: 500;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(0, 200, 255, 0.5);
  }

  to {
    box-shadow: 0 0 20px rgba(0, 200, 255, 1);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

// 巡检指标样式
.inspection-metrics {
  height: 100%;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 33%;
  position: absolute;

  &:first-child {
    left: 2%;
    top: 36%;
    transform: translateY(-50%);
  }

  &.center {
    bottom: 26px;
    left: 51%;
    transform: translateX(-50%);
    z-index: 2;

    .metric-title {
      font-size: 18px;
    }

    .metric-value {
      font-size: 22px; // PC端字体大小调整
    }
  }

  &:last-child {
    right: 2%;
    top: 36%;
    transform: translateY(-50%);
  }
}

.metric-title {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
}

.metric-value {
  font-family: TCloudNumber, TCloudNumber;
  font-weight: bold;
  font-size: 20px; // PC端字体大小调整
  text-align: center;
  font-style: normal;
  background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;

  .unit {
    font-size: 14px; // PC端字体大小调整
    margin-left: 4px;
    font-family: "Microsoft YaHei", sans-serif;
  }
}

.dangerTotal {
  background: url('@/assets/images/yibuyiping/circle.png') no-repeat center/cover;
  background-size: 100% 100%;
  width: 75px; // PC端尺寸调整
  height: 75px; // PC端尺寸调整
  position: absolute;
  left: 32%;
  top: 0%;
  font-family: TCloudNumber, TCloudNumber;
  font-weight: bold;
  font-size: 22px; // PC端字体大小调整
  color: #FFFFFF;
  text-align: center;
  font-style: normal;
  text-transform: none;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.inspection-boxes {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 10px;
  margin-top: 6px;
}

.inspection-box {
  background: url('@/assets/images/yibuyiping/num.png') no-repeat center/cover;
  background-size: 100% 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20%;
}

.box-left {
  font-size: 14px;
  color: #FFFFFF;
}

.box-right {
  display: flex;
  align-items: baseline;
}

.box-right .num {
  font-family: TCloudNumber, TCloudNumber;
  font-weight: bold;
  font-size: 20px;
  color: #0085FF;
}

.box-right .unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 2px;
}

.numOfTime-container {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  margin-top: 3px;
}

.numOfTime {
  width: 20%;
  height: 27px;
  background: url('@/assets/images/yibuyiping/cishu.png') no-repeat center/cover;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.safety-education-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 100%;
  gap: 10px;
}

.safety-education-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48%;
  height: 80%;
  margin: auto 0;
  background: url('@/assets/images/yibuyiping/safetyEducation.png') no-repeat center/cover;
  background-size: 100% 100%;
  padding: 10px;
}

.safety-label {
  margin-top: -20px;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.safety-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.safety-number {
  font-family: TCloudNumber, TCloudNumber;
  font-weight: bold;
  font-size: 24px; // PC端字体大小调整
  background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.safety-unit {
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  margin-left: 4px;
}

.safety-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 10px;
}

.left-panel .stat-card:nth-child(1) {
  flex: 1.5;
  /* 人员管理更高 */
}

.left-panel .stat-card:nth-child(2) {
  flex: 1.4;
  /* 项目检查更高 */
}

.left-panel .stat-card:nth-child(3) {
  flex: 0.7;
  /* 安全教育更低 */
}

.left-panel .stat-card:nth-child(4) {
  flex: 0.9;
  /* 隐患总览更低 */
}

.right-panel .stat-card:nth-child(1) {
  flex: 1.5;
  /* 质量管理更高 */
}

.right-panel .stat-card:nth-child(2),
.right-panel .stat-card:nth-child(3),
.right-panel .stat-card:nth-child(4) {
  flex: 1;
}

// 质量问题趋势样式
.trend-chart-container {
  margin: 0 auto;
  width: 90%;
  height: 78%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  position: relative;
  background: url('@/assets/images/yibuyiping/qualityTop.png') no-repeat center;
  background-size: 100% 100%;

  .legend-left,
  .legend-right {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #ffffff;
      font-size: 14px;

      .legend-dot {
        width: 12px;
        height: 12px;

        &.overdue {
          background: #FF8F39;
        }

        &.normal {
          background: #2EB9FF;
        }
      }
    }
  }

  .trend-chart {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
  }
}

.trend-status-container {
  width: 90%;
  height: 28%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 10px;
  flex-wrap: nowrap;
  background: url('@/assets/images/yibuyiping/twoCenter.png') no-repeat center;
  background-size: 100% 100%;

  .status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    min-width: 0;
    height: 100%;
    white-space: nowrap;
    margin-right: 30px;

    .status-icon {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      img {
        width: 24px;
        height: 24px;
      }
    }

    .status-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      min-width: 0;

      .status-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .status-number {
        font-family: TCloudNumber, TCloudNumber;
        font-weight: bold;
        font-size: 20px;
        color: #FF8F39;
        white-space: nowrap;
      }
    }
  }
}

.awards-scroll-container {
  height: 100%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

/* 渐变遮罩 */
.scroll-mask {
  position: absolute;
  left: 0;
  right: 0;
  height: 20px;
  z-index: 10;
  pointer-events: none;
}

.scroll-mask-top {
  top: 0;
  background: linear-gradient(to bottom,
      rgba(5, 30, 60, 1) 0%,
      rgba(5, 30, 60, 0.8) 50%,
      rgba(5, 30, 60, 0) 100%);
}

.scroll-mask-bottom {
  bottom: 0;
  background: linear-gradient(to top,
      rgba(5, 30, 60, 1) 0%,
      rgba(5, 30, 60, 0.8) 50%,
      rgba(5, 30, 60, 0) 100%);
}

.awards-list {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  animation: scrollUpSmooth 30s linear infinite;
  transition: all 0.3s ease;
}

/* 鼠标悬停时暂停动画 */
.awards-list.paused {
  animation-play-state: paused;
}

.award-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  margin: 8px 0;
  height: 45px;
  background: linear-gradient(89deg, rgba(0, 75, 151, 0) 0%, rgba(12, 65, 125, 0.72) 100%);
  border-radius: 6px;
  border: 1px solid;
  border-image: linear-gradient(271deg, rgba(83, 193, 255, 0.45), rgba(83, 193, 255, 0)) 1 1;
  transition: all 0.3s ease;

  /* 悬停效果 */
  &:hover {
    background: linear-gradient(89deg, rgba(0, 85, 171, 0.2) 0%, rgba(12, 65, 125, 0.9) 100%);
    border-image: linear-gradient(271deg, rgba(83, 193, 255, 0.8), rgba(83, 193, 255, 0.3)) 1 1;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 200, 255, 0.2);
  }
}

.award-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  transition: transform 0.3s ease;

  svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
  }
}

.award-item:hover .award-icon {
  transform: scale(1.1) rotate(5deg);
}

.award-name {
  flex: 1;
  font-size: 14px;
  color: #ffffff;
  margin-left: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  transition: color 0.3s ease;
}

.award-item:hover .award-name {
  color: #00c8ff;
}

.award-count {
  display: flex;
  align-items: baseline;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.award-item:hover .award-count {
  transform: scale(1.05);
}

.count-number {
  font-family: TCloudNumber, TCloudNumber;
  font-weight: bold;
  font-size: 22px;
  color: #fff;
  transition: color 0.3s ease;
}

.award-item:hover .count-number {
  color: #00c8ff;
}

.count-unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 2px;
  transition: color 0.3s ease;
}

.award-item:hover .count-unit {
  color: rgba(255, 255, 255, 0.9);
}

/* 优化的滚动动画 */
@keyframes scrollUpSmooth {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-50%);
  }
}

/* 不同级别奖项的特殊样式 */
.award-level-national,
.award-level-luban {
  border-image: linear-gradient(271deg, rgba(255, 215, 0, 0.6), rgba(255, 215, 0, 0.2)) 1 1 !important;

  &:hover {
    background: linear-gradient(89deg, rgba(255, 215, 0, 0.1) 0%, rgba(12, 65, 125, 0.9) 100%) !important;
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4) !important;
  }

  .award-name {
    font-weight: 600;
  }
}

.award-level-provincial {
  border-image: linear-gradient(271deg, rgba(192, 192, 192, 0.6), rgba(192, 192, 192, 0.2)) 1 1 !important;
}

.award-level-innovation {
  border-image: linear-gradient(271deg, rgba(155, 89, 182, 0.6), rgba(155, 89, 182, 0.2)) 1 1 !important;
}

.award-level-environmental {
  border-image: linear-gradient(271deg, rgba(39, 174, 96, 0.6), rgba(39, 174, 96, 0.2)) 1 1 !important;
}

/* 响应式优化 */
@media (max-height: 800px) {
  .awards-list {
    animation-duration: 25s;
  }

  .award-item {
    height: 40px;
    margin: 6px 0;
  }
}

@media (max-height: 600px) {
  .awards-list {
    animation-duration: 20s;
  }

  .award-item {
    height: 35px;
    margin: 4px 0;
  }
}

/* 高性能模式 */
@media (prefers-reduced-motion: reduce) {
  .awards-list {
    animation-duration: 60s;
  }
}

.legal-regulations-container {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/yibuyiping/lawer.png') no-repeat center/cover;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: flex-end;
  position: relative;
  padding: 16px 0;
}

.legal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 50%;
}

.legal-label {
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 20px;
}

.legal-count {
  display: flex;
  align-items: baseline;
  padding-right: 15px;
}

.legal-number {
  font-family: TCloudNumber, TCloudNumber;
  font-weight: bold;
  font-size: 24px;
  background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.legal-unit {
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 4px;
}
</style>