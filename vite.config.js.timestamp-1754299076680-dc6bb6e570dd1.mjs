// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
import AutoImport from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1/node_modules/unplugin-vue-components/dist/vite.mjs";
import { ElementPlusResolver } from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1/node_modules/unplugin-vue-components/dist/resolvers.mjs";
import { createSvgIconsPlugin } from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import path from "path";
import { viteZip } from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1/node_modules/vite-plugin-zip-file/lib/index.mjs";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Desktop\\zjsj-front-global-manage-1";
var useZip = process.env.VITE_USE_ZIP === "true";
var vite_config_default = defineConfig({
  build: {
    minify: "terser",
    sourcemap: false,
    // 不生成 source map（可减少体积）
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ["console.log"]
        // 彻底去除 console.log
      },
      format: {
        comments: false
        // 删除注释
      }
    },
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("three")) {
            return "three";
          }
          if (id.includes("node_modules")) {
            return "vendor";
          }
        }
      }
    },
    brotliSize: false
    // 关闭 brotli 分析，加快构建速度
  },
  css: {
    minify: true,
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ["legacy-js-api", "color-functions"]
      }
    }
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()]
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    }),
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), "src/assets/images/svg")],
      symbolId: "icon-[name]"
    }),
    ...useZip ? [viteZip({
      folderPath: path.resolve(__vite_injected_original_dirname, "dist"),
      outPath: path.resolve(__vite_injected_original_dirname),
      withoutMainFolder: true,
      zipName: (() => {
        const now = /* @__PURE__ */ new Date();
        const pad = (n) => n.toString().padStart(2, "0");
        const y = now.getFullYear();
        const m = pad(now.getMonth() + 1);
        const d = pad(now.getDate());
        const h = pad(now.getHours());
        const min = pad(now.getMinutes());
        const s = pad(now.getSeconds());
        return `\u7ECF\u8425\u5927\u5C4F-${y}${m}${d}-${h}${min}${s}.zip`;
      })()
    })] : []
  ],
  resolve: {
    extensions: [".js", ".vue", ".json", ".scss", ".css"],
    alias: {
      "@": resolve(__vite_injected_original_dirname, "src")
    }
  },
  base: "./",
  server: {
    host: "0.0.0.0",
    hmr: true,
    proxy: {
      "/admin-api": {
        target: "http://*************:31822",
        changeOrigin: true,
        rewrite: (path2) => path2
      },
      "/oauth": {
        target: "https://api2.hik-cloud.com",
        //海康云牟
        changeOrigin: true
      },
      "/v1": {
        target: "https://api2.hik-cloud.com",
        //海康云牟
        changeOrigin: true
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
