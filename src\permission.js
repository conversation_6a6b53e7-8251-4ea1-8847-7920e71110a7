import router from './router'
import { clearTokenRefresh, isRelogin } from '@/utils/request'
import { ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { setupTokenRefresh } from '@/utils/request'
import axios from 'axios'

// 白名单路由
const whiteList = ['/login']

// 自动登录触发路径
const autoLoginTriggerPath = '/noAuth'

// 支持的跳转目标页面映射
const redirectPageMap = {
    'detail': '/yibuyiping/yibuyipingDetail', // 一部一屏详情页面（主要目标）
    'anquan': '/yibuyiping/anquan',  // 质量安全部页面
    'zhiliang': '/yibuyiping/zhiliang', // 质量管理页面
    'huanjing': '/yibuyiping/huanjing', // 环境管理页面
    'guonei': '/yibuyiping/guonei',   // 国内业务页面
    'guoji': '/yibuyiping/guoji',     // 国际业务页面
    'default': '/project/index'        // 默认跳转页面
}

// 检查是否精确匹配路径
const isExactPath = (path, target) => {
    return path === target ||
        path === target + '/' ||
        path === '#' + target;
}

// 处理未授权的情况
const handleAuthorized = () => {
    if (!isRelogin.show) {
        if (window.location.href.includes('login?redirect=')) {
            return
        }
        isRelogin.show = true
        ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
            showCancelButton: false,
            closeOnClickModal: false,
            showClose: false,
            confirmButtonText: '重新登录',
            type: 'warning'
        }).then(() => {
            clearTokenRefresh()
            isRelogin.show = false
            router.push('/login')
        })
    }
    return Promise.reject('登录状态已过期')
}

// 自动登录功能
const autoLogin = async (redirectTarget = 'default') => {
    try {
        console.log('开始自动登录...', '跳转目标:', redirectTarget);
        // 调用登录接口
        const response = await axios.post(`${window.baseEnv.baseURL}/system/auth/login`, {
            username: 'admin',
            password: 'Admin258369'
        }, {
            headers: {
                'Content-Type': 'application/json',
                'tenant-id': '1'
            }
        });

        if (response.data.code === 0) {
            // 登录成功，设置token
            setupTokenRefresh(response.data.data.accessToken, response.data.data.refreshToken);
            localStorage.setItem("currentActiveTab", '业务布局');

            // 根据跳转目标设置不同的currentActiveTab
            if (redirectTarget !== 'default') {
                localStorage.setItem("currentActiveTab", '一部一屏');
            }

            console.log('路由自动登录成功，即将跳转到:', redirectPageMap[redirectTarget] || redirectPageMap.default);
            return { success: true, redirectPath: redirectPageMap[redirectTarget] || redirectPageMap.default };
        } else {
            console.error('路由自动登录失败:', response.data.msg);
            return { success: false };
        }
    } catch (error) {
        console.error('路由自动登录异常:', error);
        return { success: false };
    }
};

// 路由守卫 - 跳过所有权限校验，直接放行
router.beforeEach(async (to, from, next) => {
    console.log('🚀 路由跳转:', from.path, '->', to.path);
    console.log('🔍 查询参数:', to.query);

    // 如果访问根路径，重定向到项目首页
    if (to.path === '/' || to.path === '') {
        console.log('📍 根路径访问，重定向到项目首页');
        next({ path: '/project/index' });
        return;
    }

    // 处理免登录跳转逻辑
    if (to.path === '/noAuth') {
        console.log('🎯 检测到免登录跳转请求');
        console.log('📋 查询参数详情:', JSON.stringify(to.query));
        const redirectTarget = to.query.redirect || 'default';
        console.log('🎲 重定向目标:', redirectTarget);

        try {
            console.log('🔐 开始执行自动登录...');
            const loginResult = await autoLogin(redirectTarget);
            if (loginResult.success) {
                console.log('✅ 自动登录成功，跳转到:', loginResult.redirectPath);
                next({ path: loginResult.redirectPath });
            } else {
                console.error('❌ 自动登录失败，跳转到登录页');
                next({ path: '/login' });
            }
        } catch (error) {
            console.error('💥 自动登录过程中发生错误:', error);
            next({ path: '/login' });
        }
        return;
    }

    console.log(to.path)
    // 业务展示页面特殊处理 - 确保始终可以访问
    if (to.path.startsWith('/business-display') || to.path.startsWith('/video-display')) {
        console.log('业务展示页面访问，直接放行:', to.path);
        next();
        return;
    }

    // 一部一屏页面特殊处理 - 确保始终可以访问
    if (to.path.startsWith('/yibuyiping')) {
        console.log('一部一屏页面访问，直接放行:', to.path);
        next();
        return;
    }

    // 所有其他路由直接放行，不进行权限校验
    next();
})