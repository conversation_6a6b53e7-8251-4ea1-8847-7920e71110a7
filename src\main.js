import { createApp } from "vue";
import "./style.css";
import App from "./App.vue";
import router from "./router";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import "element-plus/theme-chalk/dark/css-vars.css";
import * as echarts from "echarts";
import "./permission" // 添加权限控制
import TitleBar from "./components/TitleBar.vue";
import "./assets/fonts/font.css";
import "echarts-liquidfill";
import SvgIcon from "./components/SvgIcon.vue";
import 'virtual:svg-icons-register' // 必须！用于注入 symbol
import request from "./utils/request";
import { setupTokenRefresh } from "./utils/request";
import axios from "axios";
import { fixEZUIKitUnloadError, cleanupEZUIKitResources } from "./utils/ezuikitFix.js";
import { initErrorFix } from "./utils/chartErrorFix.js";
import i18n from "./i18n";

window.zjRequest = request;

// 自动登录功能 - 增强的健壮性版本
const autoLogin = async (retryCount = 0) => {
    // 检查是否已经有token
    if (!sessionStorage.getItem('token')) {
        try {
            // 标记自动登录进行中
            sessionStorage.setItem('autoLoginInProgress', 'true');

            // 检查当前访问的页面类型，对业务展示页面使用更宽松的处理
            const currentPath = window.location.hash.split('?')[0]
            const isBusinessDisplayPage = currentPath.includes('/business-display') || currentPath.includes('/video-display') ||
                window.location.pathname.includes('/business-display') || window.location.pathname.includes('/video-display')

            console.log(`自动登录尝试 (${retryCount + 1}/3), 当前页面: ${currentPath}, 是否为业务展示页面: ${isBusinessDisplayPage}`);

            // 调用登录接口
            const response = await axios.post(`${window.baseEnv.baseURL}/system/auth/login`, {
                username: 'admin',
                password: 'Admin258369'
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'tenant-id': '1'
                },
                timeout: isBusinessDisplayPage ? 3000 : 5000 // 业务展示页面使用更短的超时时间
            });

            if (response.data.code === 0) {
                // 登录成功，设置token
                setupTokenRefresh(response.data.data.accessToken, response.data.data.refreshToken);
                localStorage.setItem("currentActiveTab", '业务布局');
                console.log('自动登录成功');
                return true;
            } else {
                console.error('自动登录失败:', response.data.msg);

                // 对于业务展示页面，登录失败不影响页面访问
                if (isBusinessDisplayPage) {
                    console.warn('业务展示页面自动登录失败，但允许继续访问');
                    return true; // 返回true，表示可以继续
                }

                // 尝试重试
                if (retryCount < 2) { // 减少重试次数到2次
                    console.log(`重试自动登录(${retryCount + 1}/2)...`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return await autoLogin(retryCount + 1);
                }
                return false;
            }
        } catch (error) {
            console.error('自动登录异常:', error);

            // 检查当前访问的页面类型
            const currentPath = window.location.hash.split('?')[0]
            const isBusinessDisplayPage = currentPath.includes('/business-display') || currentPath.includes('/video-display') ||
                window.location.pathname.includes('/business-display') || window.location.pathname.includes('/video-display')

            // 对于业务展示页面，网络错误不影响页面访问
            if (isBusinessDisplayPage) {
                console.warn('业务展示页面自动登录网络异常，但允许继续访问');
                return true; // 返回true，表示可以继续
            }

            // 尝试重试
            if (retryCount < 2) { // 减少重试次数到2次
                console.log(`重试自动登录(${retryCount + 1}/2)...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return await autoLogin(retryCount + 1);
            }
            return false;
        } finally {
            if (retryCount === 0 || retryCount >= 2) {
                // 只有在首次尝试或最后一次重试后才清除标记
                sessionStorage.removeItem('autoLoginInProgress');
            }
        }
    }
    return true; // 已经有token，不需要登录
};

// 执行自动登录并初始化应用
const initApp = async () => {
    try {
        // 初始化错误修复系统，过滤Chrome扩展相关错误
        initErrorFix();

        // 全局修复EZUIKit页面卸载错误
        fixEZUIKitUnloadError();

        await autoLogin();
    } catch (error) {
        console.error('初始化自动登录失败:', error);
    } finally {
        // 无论登录成功与否，都初始化应用
        const app = createApp(App);
        app.use(ElementPlus, { locale: zhCn });
        app.component('svg-icon', SvgIcon) // 注册全局组件

        app.component("TitleBar", TitleBar);
        app.use(router);
        app.use(i18n);
        app.mount("#app");
    }
};

// 页面卸载时清理EZUIKit资源
window.addEventListener('beforeunload', () => {
    cleanupEZUIKitResources();
});

// 启动应用
initApp();
