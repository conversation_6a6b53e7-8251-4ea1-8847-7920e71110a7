<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  barData: {
    type: Object,
    required: true
  },
  lineData: {
    type: Object,
    required: true
  },
  xData: {
    type: Array,
    default: () => []
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  }
});

const chartContainer = ref(null);
let chart = null;

const initChart = () => {
  if (!chartContainer.value) return;
  
  chart = echarts.init(chartContainer.value);
  updateChart();
};

const updateChart = () => {
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          show: false
        },
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      backgroundColor: 'rgba(0,0,0,0.3)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      top: '10%',
      left: '3%',
      right: '3%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.xData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12
        }
      },
      {
        type: 'value',
        name: '百分比(%)',
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 12
        },
        min: 0,
        max: 100,
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          formatter: '{value}%',
          fontSize: 12
        }
      }
    ],
    series: [
      {
        name: props.barData.name,
        type: 'bar',
        data: props.barData.data,
        barWidth: '30%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(233,54,40, 0.9)' },
            { offset: 1, color: 'rgba(233,54,40, 0.5)' }
          ])
        }
      },
      {
        name: props.lineData.name,
        type: 'line',
        yAxisIndex: 1,
        data: props.lineData.data,
        symbol: 'circle',
        symbolSize: 8,
        smooth: true,
        itemStyle: {
          color: 'rgb(0,152,101)'
        },
        lineStyle: {
          width: 3,
          color: 'rgb(0,152,101)'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0,152,101, 0.6)' },
            { offset: 1, color: 'rgba(0,152,101, 0.1)' }
          ])
        }
      }
    ]
  };
  
  chart.setOption(option);
};

watch(
  () => [props.barData, props.lineData, props.xData],
  () => {
    updateChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
  window.addEventListener('resize', () => chart?.resize());
});

onUnmounted(() => {
  chart?.dispose();
  window.removeEventListener('resize', () => chart?.resize());
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style> 