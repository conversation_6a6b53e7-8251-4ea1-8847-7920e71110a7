<template>
  <div class="yibuyiping-detail-container">
    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img src="@/assets/images/header/back.png" alt="" />
      <span>返回</span>
    </div>
    
    <div class="main-content">
      <!-- 左侧板块 -->
      <div class="left-panel">
        <!-- 国内板块 -->
        <div class="board-card domestic-board">
          <div class="board-header">
            <span class="board-title">国内板块</span>
          </div>
          <div class="board-content">
            <div class="bg-div">
              <div class="dynamic-text">国内业务发展</div>
            </div>
          </div>
        </div>

        <!-- 国际板块 -->
        <div class="board-card international-board">
          <div class="board-header">
            <span class="board-title">国际板块</span>
          </div>
          <div class="board-content">
            <div class="bg-div"><div class="dynamic-text">海一</div></div>
            <div class="bg-div"><div class="dynamic-text">海二</div></div>
          </div>
        </div>

        <!-- 贸易板块 -->
        <div class="board-card trade-board">
          <div class="board-header">
            <span class="board-title">贸易板块</span>
          </div>
          <div class="board-content">
            <div class="bg-div">
              <div class="trade-list">
                <div class="trade-item">
                  <span>仓储</span>
                </div>
                <div class="trade-item">
                  <span>实验室</span>
                </div>
                <div class="trade-item">
                  <span>制度</span>
                </div>
                <div class="trade-item">
                  <span>管理人员</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 投资板块 -->
        <div class="board-card investment-board">
          <div class="board-header">
            <span class="board-title">投资板块</span>
          </div>
          <div class="board-content">
            <div class="bg-div">
              <div class="dynamic-text">国投公司</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中央地图区域 -->
      <div class="center-map">
        <div class="map-container">

          <!-- 底部按钮 -->
          <div class="bottom-buttons">
            <div class="action-button safety" @click="goToSafety">
              <img src="@/assets/images/yibuyiping/secure.png" alt="安全">
            </div>
            <div class="action-button quality" @click="goToQuality">
              <img src="@/assets/images/yibuyiping/quality.png" alt="质量">
            </div>
            <div class="action-button environment" @click="goToEnvironment">
              <img src="@/assets/images/yibuyiping/environment.png" alt="环境">
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧板块 -->
      <div class="right-panel">
        <!-- 劳务板块 -->
        <div class="board-card labor-board">
          <div class="board-header">
            <span class="board-title">劳务板块</span>
          </div>
          <div class="board-content">
            <div class="bg-div">
              <div class="dynamic-text">国内劳务</div>
              <div class="board-stats">
                <div class="stat-item">
                  <div class="stat-number">3000<span class="stat-unit">人</span></div>
                </div>
              </div>
            </div>
            <div class="bg-div">
              <div class="dynamic-text">国际劳务</div>
              <div class="board-stats">
                <div class="stat-item">
                  <div class="stat-number">5000<span class="stat-unit">人</span></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 制造业板块 -->
        <div class="board-card manufacturing-board">
          <div class="board-header">
            <span class="board-title">制造业板块</span>
          </div>
          <div class="board-content">
            <div class="bg-div"><div class="dynamic-text">中江装配</div></div>
          </div>
        </div>

        <!-- 其他板块 -->
        <div class="board-card other-board">
          <div class="board-header">
            <span class="board-title">其他板块</span>
          </div>
          <div class="board-content">
            <div class="bg-div"><div class="dynamic-text">物业公司</div></div>
          </div>
        </div>

        <!-- 创优板块 -->
        <div class="board-card excellence-board">
          <div class="board-header">
            <span class="board-title">创优板块</span>
          </div>
          <div class="board-content">
            <div class="content-overlay">
              <div class="awards-scroll-container" @mouseenter="pauseScrolling" @mouseleave="resumeScrolling">
              <!-- 顶部渐变遮罩 -->
              <div class="scroll-mask scroll-mask-top"></div>
              <!-- 底部渐变遮罩 -->
              <div class="scroll-mask scroll-mask-bottom"></div>

              <div class="awards-list" ref="awardsListRef" :class="{ paused: isScrollPaused }">
                <!-- 第一组数据 -->
                <div class="award-item" v-for="(award, index) in awardsList" :key="`award-first-${index}`"
                  :class="`award-level-${award.level}`">
                  <div class="award-name">{{ award.name }}</div>
                  <div class="award-count">
                    <span class="count-number">{{ award.count }}</span>
                    <span class="count-unit">个</span>
                  </div>
                </div>
                <!-- 第二组数据（无缝循环） -->
                <div class="award-item" v-for="(award, index) in awardsList" :key="`award-second-${index}`"
                  :class="`award-level-${award.level}`">
                  <div class="award-name">{{ award.name }}</div>
                  <div class="award-count">
                    <span class="count-number">{{ award.count }}</span>
                    <span class="count-unit">个</span>
                  </div>
                </div>
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 管理按钮组 - 位于右侧板块上方 -->
    <div class="management-buttons">
      <div class="button-item" @click="goToProjectDashboard">
        <span>项目看板</span>
        <img src="@/assets/images/yibuyiping/jumpIcon.png" class="arrow-icon" alt="跳转">
      </div>
      <div class="button-item" @click="goToSafetyPortal">
        <span>安全门户</span>
        <img src="@/assets/images/yibuyiping/jumpIcon.png" class="arrow-icon" alt="跳转">
      </div>
      <div class="button-item" @click="goToBackendManagement">
        <span>后台管理</span>
        <img src="@/assets/images/yibuyiping/jumpIcon.png" class="arrow-icon" alt="跳转">
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import axios from 'axios';

const router = useRouter();
const { t } = useI18n();

// 清理localStorage中的token (可选)
const clearStoredTokens = () => {
  localStorage.removeItem('menhu_token');
  localStorage.removeItem('index_token');
  console.log('已清理存储的tokens');
};

// 获取登录token的函数
const getAuthToken = async () => {
  try {
    const response = await axios.post('http://*************:8084/login', {
      username: 'admin',
      password: 'admin123',
    });
    
    if (response.data && response.data.code === 200 && response.data.token) {
      return response.data.token;
    } else {
      console.error('获取token失败:', response.data);
      return null;
    }
  } catch (error) {
    console.error('登录接口调用失败:', error);
    return null;
  }
};

/* 
备选方案注释：
1. 如果目标网站不支持URL参数，可以改用window.open()在新窗口打开，保持原页面的sessionStorage
2. 如果需要更安全的传递，可以使用POST请求将token发送到目标网站的登录接口
3. 如果是同一个域名下的不同子域，可以设置domain cookie
*/

const goBack = () => {
  router.push('/yibuyiping/index');
};

// 跳转到安全页面
const goToSafety = () => {
  router.push('/yibuyiping/anquan'); // 根据实际路由路径调整
};

// 跳转到质量页面
const goToQuality = () => {
  router.push('/yibuyiping/zhiliang'); // 根据实际路由路径调整
};

// 跳转到环境页面
const goToEnvironment = () => {
  router.push('/yibuyiping/huanjing'); // 根据实际路由路径调整
};

// 跳转到项目看板
const goToProjectDashboard = () => {
  window.location.href = 'http://*************:8098/#/noAuth';
};

// 跳转到安全门户 - 实现免登录
const goToSafetyPortal = async () => {
  try {
    // 获取token
    const token = await getAuthToken();
    if (token) {
      // 方案1: URL参数传递 (推荐)http://*************:8085/login?redirect=%2Findex
      const targetUrl = `http://*************:81/menhu?token=${encodeURIComponent(token)}&source=menhu`;
      
      // 方案2: 同时存储到localStorage作为备用
      localStorage.setItem('menhu_token', token);
      
      console.log('安全门户token准备传递:', token);
      console.log('跳转URL:', targetUrl);
      
      // 跳转到安全门户
      window.location.href = targetUrl;
    } else {
      console.error('获取token失败，无法进行免登录跳转');
      alert('获取认证信息失败，请稍后重试');
    }
  } catch (error) {
    console.error('安全门户跳转失败:', error);
    alert('跳转失败，请稍后重试');
  }
};

// 跳转到后台管理 - 实现免登录
const goToBackendManagement = async () => {
  try {
    // 获取token
    const token = await getAuthToken();
    if (token) {
      // 方案1: URL参数传递 (推荐)
      const targetUrl = `http://*************:81/index?token=${encodeURIComponent(token)}&source=index`;
      
      // 方案2: 同时存储到localStorage作为备用
      localStorage.setItem('index_token', token);
      
      console.log('后台管理token准备传递:', token);
      console.log('跳转URL:', targetUrl);
      
      // 跳转到后台管理
      window.location.href = targetUrl;
    } else {
      console.error('获取token失败，无法进行免登录跳转');
      alert('获取认证信息失败，请稍后重试');
    }
  } catch (error) {
    console.error('后台管理跳转失败:', error);
    alert('跳转失败，请稍后重试');
  }
};



// 奖项数据
const awardsList = ref([
  { name: t('oneOfficeOneScreen.detail.awards.nationalQualityAward'), count: 2, level: 'national' },
  { name: t('oneOfficeOneScreen.detail.awards.provincialSafetyAward'), count: 5, level: 'provincial' },
  { name: t('oneOfficeOneScreen.detail.awards.municipalGreenBuildingAward'), count: 3, level: 'municipal' },
  { name: t('oneOfficeOneScreen.detail.awards.industryQualityAward'), count: 1, level: 'industry' },
  { name: t('oneOfficeOneScreen.detail.awards.technicalInnovationAward'), count: 4, level: 'innovation' },
  { name: t('oneOfficeOneScreen.detail.awards.environmentalProtectionAward'), count: 2, level: 'environmental' },
  { name: t('oneOfficeOneScreen.detail.awards.smartConstructionAward'), count: 1, level: 'smart' },
  { name: t('oneOfficeOneScreen.detail.awards.lubanAward'), count: 1, level: 'luban' }
]);

// 滚动控制
const isScrollPaused = ref(false);
const awardsListRef = ref(null);
let scrollObserver = null;

// 暂停滚动
const pauseScrolling = () => {
  isScrollPaused.value = true;
};

// 恢复滚动
const resumeScrolling = () => {
  isScrollPaused.value = false;
};



// 智能滚动控制：当页面不可见时暂停动画
const setupScrollOptimization = () => {
  // 监听页面可见性变化
  const handleVisibilityChange = () => {
    if (document.hidden) {
      isScrollPaused.value = true;
    } else {
      isScrollPaused.value = false;
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);

  // 使用 Intersection Observer 监听容器是否在视口中
  if (awardsListRef.value) {
    scrollObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (!entry.isIntersecting && !document.hidden) {
            isScrollPaused.value = true;
          } else if (entry.isIntersecting && !document.hidden) {
            isScrollPaused.value = false;
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    scrollObserver.observe(awardsListRef.value.parentElement);
  }

  // 清理函数
  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    if (scrollObserver) {
      scrollObserver.disconnect();
    }
  };
};













let cleanupScrollOptimization = null;

onMounted(() => {
  // 设置滚动优化
  setTimeout(() => {
    cleanupScrollOptimization = setupScrollOptimization();
  }, 100);
});

// 组件卸载时清理
onUnmounted(() => {
  if (cleanupScrollOptimization) {
    cleanupScrollOptimization();
  }
});
</script>

<style scoped lang="scss">
.yibuyiping-detail-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: url('@/assets/images/yibuyiping/safety.png') no-repeat center center;
  background-size: cover;
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
  pointer-events: all;
}

// 板块卡片基础样式
.board-card {
  background: rgba(5, 30, 60, 0.8);
  border: 1px solid rgba(0, 200, 255, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  flex: 1;
  min-height: 200px;
  margin-bottom: 10px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 200, 255, 0.2);
  }

  .board-header {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding-left: 20px;
    background: rgba(0, 200, 255, 0.1);
    position: relative;

    .board-title {
      font-family: "Alimama", sans-serif;
      font-weight: 700;
      font-size: 16px;
      background-image: linear-gradient(to bottom,
          #006FD0 0%,
          #FFFFFF 50%,
          #006FD0 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }
  }

  .board-content {
    width: 100%;
    height: calc(100% - 40px);
    padding: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;

    .dynamic-text {
      font-size: 16px;
      color: #ffffff;
    }

    .board-stats {
      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;

        .stat-number {
          font-family: TCloudNumber, TCloudNumber;
          font-weight: bold;
          font-size: 32px;
          background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          animation: numberPulse 3s ease-in-out infinite;
        }

        .stat-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

// 使用 long.png 背景的板块 - 应用到bg-div上
.domestic-board,
.investment-board,
.manufacturing-board,
.other-board {
  .board-content {
    width: 100%;
    height: calc(100% - 40px);
    padding: 20px;
    
    .bg-div {
      width: 100%;
      height: 100%;
      background: url('@/assets/images/yibuyiping/long.png') no-repeat center/cover;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      
      .dynamic-text {
        font-size: 18px;
        color: #ffffff;
      }

      .board-stats {
        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 10px;

          .stat-number {
            font-family: TCloudNumber, TCloudNumber;
            font-weight: bold;
            font-size: 32px;
            background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: numberPulse 3s ease-in-out infinite;
          }

          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }
  }
}

// 国际板块和劳务板块的特殊结构
.international-board,
.labor-board {
  .board-content {
    width: 100%;
    height: calc(100% - 40px);
    display: flex;
    flex-direction: row;
    gap: 20px;
    padding: 10px;
    
    .bg-div {
      flex: 1;
      height: 100%;
      min-height: 120px;
      background: url('@/assets/images/yibuyiping/short.png') no-repeat center/cover;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 10px;
      
      .dynamic-text {
        font-size: 16px;
        color: #ffffff;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .board-stats {
        margin-top: 20px;
        
        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 5px;

          .stat-number {
            font-family: TCloudNumber, TCloudNumber;
            font-weight: bold;
            font-size: 28px;
            background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: numberPulse 3s ease-in-out infinite;
            
            .stat-unit {
              font-size: 16px;
              margin-left: 2px;
              color: #ffffff;
              -webkit-text-fill-color: #ffffff;
            }
          }

          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }
  }
}

// 贸易板块使用 tradeDisplay.png 背景 - 应用到bg-div上
.trade-board {
  // 贸易板块高度增加
  flex: 1.8;
  
  .board-content {
    padding: 0;
    height: calc(100% - 40px);
    
    .bg-div {
      width: 100%;
      height: 100%;
      background: 
        url('@/assets/images/yibuyiping/company.png') 12px 4px/60px 20px no-repeat,
        url('@/assets/images/yibuyiping/tradeDisplay.png') center/cover no-repeat;
      background-color: rgba(0, 40, 80, 0.9);
      display: flex;
      flex-direction: column;
      padding: 20px 15px 15px 15px;
      
      .trade-title {
        background: rgba(0, 100, 200, 0.8);
        color: #ffffff;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        padding: 8px 12px;
        margin-bottom: 15px;
        border-radius: 4px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        border: 1px solid rgba(0, 200, 255, 0.5);
      }
      
      .trade-list {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-top: 10px;
        
        .trade-item {
          display: flex;
          align-items: center;
          background: url('@/assets/images/yibuyiping/tradeDisplay.png') no-repeat center/cover;
          border: 1px solid rgba(0, 200, 255, 0.3);
          border-radius: 6px;
          padding: 12px 15px;
          height: 40px;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
          justify-content: center;
          span {
            color: #ffffff;
            font-size: 15px;
            font-weight: 500;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
          }
          
          &:hover {
            border-color: rgba(0, 200, 255, 0.6);
            background-color: rgba(0, 200, 255, 0.1);
            transform: translateX(3px);
          }
        }
      }
    }
  }
}

// 投资板块高度减小
.investment-board {
  flex: 0.5;
}

// 创优板块特殊样式
.excellence-board {
  .board-content {
    width: 100%;
    height: calc(100% - 40px);
    padding: 10px;
    
    .bg-div {
      width: 100%;
      height: 100%;
      background: rgba(0, 40, 80, 0.6);
      padding: 0;
    }
    
    .content-overlay {
      width: 100%;
      height: 100%;
      pointer-events: auto;
    }
  }
}

// 动态文字效果
@keyframes textGlow {
  from {
    text-shadow: 0 0 10px rgba(0, 200, 255, 0.5);
  }
  to {
    text-shadow: 0 0 20px rgba(0, 200, 255, 1), 0 0 30px rgba(0, 200, 255, 0.5);
  }
}

// 数字脉冲效果
@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
.back-button {
    position: absolute;
    top: 30px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
    z-index: 1000;
}


.header-nav {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(0, 20, 40, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  z-index: 100;
  border-bottom: 1px solid rgba(0, 200, 255, 0.3);

  .nav-item {
    color: #ffffff;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;

    &:hover {
      background: rgba(0, 200, 255, 0.2);
    }

    &.active {
      background: rgba(0, 200, 255, 0.3);
      color: #00c8ff;
    }

    .back-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }

  .nav-group {
    display: flex;
    gap: 20px;

    &.right {
      gap: 30px;
    }
  }

  .title-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: #00c8ff;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 200, 255, 0.5);
  }
}

.main-content {
  margin-top: 120px;
  display: flex;
  gap: 20px;
  padding: 0 20px 20px 20px;
  height: calc(100% - 120px);
}

.left-panel,
.right-panel {
  width: 21%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
}

// 管理按钮组样式 - 简化版本
.management-buttons {
  position: absolute;
  top: 70px;
  right: 20px;
  width: 21%;
  display: flex;
  justify-content: space-between;
  gap: 12px;
  z-index: 100;
  
  .button-item {
    flex: 1;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0 15px;
    gap: 8px;

    span {
      font-family: "Microsoft YaHei", sans-serif;
      font-size: 14px;
      color: #ffffff;
      font-weight: 500;
      white-space: nowrap;
    }

    .arrow-icon {
      width: 30px;
      height: 30px;
    }
  }
}

.center-map {
  flex: 1;
  display: flex;
  flex-direction: column;
}









.map-container {
  padding: 20px;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.top-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30px;

  .stat-box {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #ffffff;
    padding: 10px 15px;
    border-radius: 8px;
    background: linear-gradient(270deg, rgba(30, 79, 124, 0.7) 0%, rgba(0, 133, 255, 0) 100%);

    .icon {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      flex-shrink: 0;

      &.purple {
        background: rgba(138, 43, 226, 0.3);
      }

      &.teal {
        background: rgba(0, 210, 211, 0.3);
      }

      &.blue {
        background: rgba(55, 66, 250, 0.3);
      }
    }

    .content {
      text-align: left;
      margin-left: 10px;

      .label {
        font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
        font-weight: normal;
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .number {
        font-size: 28px;
        font-weight: bold;
        color: #fff;

        .unit {
          font-size: 14px;
          margin-left: 4px;
        }
      }
    }
  }
}

.world-map {
  flex: 1;
  position: relative;
  min-height: 400px;

  .map-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #0a1628 0%, #1a2744 100%);
    border-radius: 8px;
    overflow: hidden;
  }

  .continent {
    position: absolute;
    background: rgba(0, 150, 200, 0.3);
    border: 1px solid rgba(0, 200, 255, 0.5);
    border-radius: 20px;
    box-shadow: 0 0 15px rgba(0, 200, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 200, 255, 0.4);
      box-shadow: 0 0 25px rgba(0, 200, 255, 0.4);
    }

    &.asia {
      border-radius: 30px 10px 25px 15px;
      background: rgba(0, 180, 150, 0.3);
    }

    &.africa {
      border-radius: 15px 25px 20px 10px;
      background: rgba(200, 150, 0, 0.3);
    }

    &.europe {
      border-radius: 20px 15px 10px 20px;
      background: rgba(150, 0, 200, 0.3);
    }

    &.north-america {
      border-radius: 25px 20px 30px 15px;
      background: rgba(0, 200, 100, 0.3);
    }

    &.south-america {
      border-radius: 10px 15px 25px 20px;
      background: rgba(200, 100, 0, 0.3);
    }

    &.australia {
      border-radius: 50%;
      background: rgba(200, 0, 150, 0.3);
    }
  }

  .map-point {
    position: absolute;
    z-index: 20;

    .point-marker {
      background: rgba(0, 200, 255, 0.8);
      color: #ffffff;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      animation: pulse 2s infinite;

      &::before {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid rgba(0, 200, 255, 0.8);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: 6px;
        background: #00c8ff;
        border-radius: 50%;
        box-shadow: 0 0 10px rgba(0, 200, 255, 0.8);
        animation: glow 1.5s ease-in-out infinite alternate;
      }
    }
  }
}

.bottom-buttons {
  display: flex;
  justify-content: center;
  gap: 50px;
  margin-top: 20px;
  margin-bottom: 20px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 2%;

  .action-button {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;

    img {
      width: 100%;
      height: 100%;
      margin-bottom: 5px;
    }

    span {
      margin-top: 5px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
    }
  }
}











.awards-scroll-container {
  height: 100%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

/* 渐变遮罩 */
.scroll-mask {
  position: absolute;
  left: 0;
  right: 0;
  height: 20px;
  z-index: 10;
  pointer-events: none;
}

.scroll-mask-top {
  top: 0;
  background: linear-gradient(to bottom,
      rgba(5, 30, 60, 1) 0%,
      rgba(5, 30, 60, 0.8) 50%,
      rgba(5, 30, 60, 0) 100%);
}

.scroll-mask-bottom {
  bottom: 0;
  background: linear-gradient(to top,
      rgba(5, 30, 60, 1) 0%,
      rgba(5, 30, 60, 0.8) 50%,
      rgba(5, 30, 60, 0) 100%);
}

.awards-list {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  animation: scrollUpSmooth 30s linear infinite;
  transition: all 0.3s ease;
}

/* 鼠标悬停时暂停动画 */
.awards-list.paused {
  animation-play-state: paused;
}

.award-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  margin: 8px 0;
  height: 40px;
  background: url('@/assets/images/yibuyiping/awardBackground.png') no-repeat center/cover;
  background-size: 100% 100%;
  border-radius: 6px;
  transition: all 0.3s ease;

  /* 悬停效果 */
  &:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  }
}



.award-name {
  flex: 1;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  transition: color 0.3s ease;
}

.award-item:hover .award-name {
  color: #00c8ff;
}

.award-count {
  display: flex;
  align-items: baseline;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.award-item:hover .award-count {
  transform: scale(1.05);
}

.count-number {
  font-family: TCloudNumber, TCloudNumber;
  font-weight: bold;
  font-size: 22px;
  color: #fff;
  transition: color 0.3s ease;
}

.award-item:hover .count-number {
  color: #00c8ff;
}

.count-unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 2px;
  transition: color 0.3s ease;
}

.award-item:hover .count-unit {
  color: rgba(255, 255, 255, 0.9);
}

/* 优化的滚动动画 */
@keyframes scrollUpSmooth {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-50%);
  }
}

/* 不同级别奖项的特殊样式 */
.award-level-national,
.award-level-luban {
  &:hover {
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4) !important;
  }

  .award-name {
    font-weight: 600;
  }
}

/* 响应式优化 */
@media (max-height: 800px) {
  .awards-list {
    animation-duration: 25s;
  }

  .award-item {
    height: 40px;
    margin: 6px 0;
  }
}

@media (max-height: 600px) {
  .awards-list {
    animation-duration: 20s;
  }

  .award-item {
    height: 35px;
    margin: 4px 0;
  }
}

/* 高性能模式 */
@media (prefers-reduced-motion: reduce) {
  .awards-list {
    animation-duration: 60s;
  }
}


</style>