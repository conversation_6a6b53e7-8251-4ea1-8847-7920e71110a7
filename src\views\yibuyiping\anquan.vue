<template>
    <div class="anquan-container">
        <!-- 返回按钮 -->
        <!-- <img class="pointer-events-auto cursor-pointer absolute top-[30px] left-[60px]"
            src="@/assets/images/xiangmu/back.png" alt="" @click="back" style="z-index: 3" /> -->
            <div class="back-button" @click="back">
            <img src="@/assets/images/header/back.png" alt="" />
            <span>返回</span>
        </div>
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧区域 -->
            <div class="left-section">
                <!-- 检查类型分析 -->
                <div class="stat-card">
                    <div class="chart-header">
                        <span>{{ $t('oneOfficeOneScreen.safety.inspectionTypeAnalysis.title') }}</span>
                    </div>
                    <div class="chart-body">
                        <!-- 上部分：3D环形图 -->
                        <div class="analysis-top">
                            <SafetyRingChart :data="analysisData" height="180px" />
                        </div>

                        <!-- 下部分：三个竖着的背景图 -->
                        <div class="analysis-bottom">
                            <div class="mini-card card-1">
                                <div class="mini-content">
                                    <div class="mini-upper">
                                    </div>
                                    <div class="mini-lower">
                                        <div class="mini-row">
                                            <span>4次</span>
                                            <span>80%</span>
                                        </div>
                                        <div class="mini-label">{{
                                            $t('oneOfficeOneScreen.safety.inspectionTypeAnalysis.generalRecord') }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mini-card card-2">
                                <div class="mini-content">
                                    <div class="mini-upper">
                                    </div>
                                    <div class="mini-lower">
                                        <div class="mini-row">
                                            <span>4次</span>
                                            <span>12%</span>
                                        </div>
                                        <div class="mini-label">{{
                                            $t('oneOfficeOneScreen.safety.inspectionTypeAnalysis.hazardRectification')
                                        }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mini-card card-3">
                                <div class="mini-content">
                                    <div class="mini-upper">
                                    </div>
                                    <div class="mini-lower">
                                        <div class="mini-row">
                                            <span>4次</span>
                                            <span>8%</span>
                                        </div>
                                        <div class="mini-label">{{
                                            $t('oneOfficeOneScreen.safety.inspectionTypeAnalysis.shutdownRectification')
                                        }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 企业检查 -->
                <div class="stat-card">
                    <div class="chart-header">
                        <span>{{ $t('oneOfficeOneScreen.safety.enterpriseInspection.title') }}</span>
                    </div>
                    <div class="chart-body">
                        <div class="renyuanTop">
                            <div style="display: flex;align-items: center;">{{
                                $t('oneOfficeOneScreen.safety.enterpriseInspection.totalEnterprises') }}<span
                                    class="renyuanTop-num">2301<span class="unit">{{
                                        $t('oneOfficeOneScreen.safety.units.items') }}</span></span></div>
                        </div>
                        <div class="safety-stats-container">
                            <div class="corner-item top-left">
                                <div class="label">{{ $t('oneOfficeOneScreen.safety.enterpriseInspection.pendingRectification')
                                }}</div>
                                <div class="value">4<span class="unit-text">{{
                                    $t('oneOfficeOneScreen.safety.units.times') }}</span></div>
                            </div>

                            <div class="corner-item top-right">
                                <div class="label">{{ $t('oneOfficeOneScreen.safety.enterpriseInspection.pendingReview')
                                    }}</div>
                                <div class="value">8<span class="unit-text">{{
                                    $t('oneOfficeOneScreen.safety.units.times') }}</span></div>
                            </div>

                            <div class="corner-item bottom-left">
                                <div class="label">{{ $t('oneOfficeOneScreen.safety.enterpriseInspection.qualified')
                                    }}</div>
                                <div class="value">6<span class="unit-text">{{
                                    $t('oneOfficeOneScreen.safety.units.times') }}</span></div>
                            </div>

                            <div class="corner-item bottom-right">
                                <div class="label">{{ $t('oneOfficeOneScreen.safety.enterpriseInspection.unqualified')
                                    }}</div>
                                <div class="value">12<span class="unit-text">{{
                                    $t('oneOfficeOneScreen.safety.units.times') }}</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间区域 -->
            <div class="center-section">
                <!-- 项目检查分析 -->
                <div class="stat-card center-box">
                    <div class="chart-header project-analysis-header">
                        <span>{{ $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.title') }}</span>
                    </div>
                    <div class="chart-body">
                        <!-- 项目检查分析内容 -->
                        <div class="project-analysis-container">
                            <div class="project-item item-1">
                                <div class="project-content">
                                    <div class="project-label">{{
                                        $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.inspectionPoint') }}
                                    </div>
                                    <div class="project-value">4<span class="project-unit">{{
                                        $t('oneOfficeOneScreen.safety.units.items') }}</span></div>
                                </div>
                            </div>

                            <div class="project-item item-2">
                                <div class="project-content">
                                    <div class="project-label">{{
                                        $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.inspectionPoint') }}
                                    </div>
                                    <div class="project-value">4<span class="project-unit">{{
                                        $t('oneOfficeOneScreen.safety.units.items') }}</span></div>
                                </div>
                            </div>

                            <div class="project-item item-3">
                                <div class="project-content">
                                    <div class="project-label">{{
                                        $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.inspectionPoint') }}
                                    </div>
                                    <div class="project-value">4<span class="project-unit">{{
                                        $t('oneOfficeOneScreen.safety.units.items') }}</span></div>
                                </div>
                            </div>
                        </div>
                        <!-- 表格 -->
                        <div class="safety-table-container">
                            <div class="table-wrapper">
                                <table class="safety-table">
                                    <thead>
                                        <tr>
                                            <th>{{
                                                $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.table.inspectionTime')
                                            }}</th>
                                            <th>{{
                                                $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.table.inspectionPoint')
                                            }}</th>
                                            <th>{{
                                                $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.table.inspector')
                                            }}</th>
                                            <th>{{
                                                $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.table.inspectionSituation')
                                            }}</th>
                                            <th>{{
                                                $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.table.qrCode')
                                            }}</th>
                                            <th>{{
                                                $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.table.operation')
                                            }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(item, index) in tableData" :key="index">
                                            <td>{{ item.checkTime }}</td>
                                            <td>{{ item.checkPoint }}</td>
                                            <td>{{ item.checker }}</td>
                                            <td>{{ item.situation }}</td>
                                            <td>{{ item.qrCode }}</td>
                                            <td>
                                                <button class="action-btn">{{
                                                    $t('oneOfficeOneScreen.safety.projectInspectionAnalysis.table.search')
                                                    }}</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧区域 -->
            <div class="right-section">
                <!-- 隐患类型分析 -->
                <div class="stat-card">
                    <div class="chart-header">
                        <span>{{ $t('oneOfficeOneScreen.safety.hazardTypeAnalysis.title') }}</span>
                    </div>
                    <div class="chart-body">
                        <!-- 隐患类型分析内容 -->
                        <div class="hazard-type-container">
                            <div class="hazard-chart-wrapper">
                                <HazardDoughnutChart :data="hazardTypeData" />
                            </div>

                            <!-- <div class="hazard-item center-item">
                                <div class="hazard-label">{{
                                    $t('oneOfficeOneScreen.safety.hazardTypeAnalysis.hazardType') }}</div>
                                <div class="hazard-value">4<span class="hazard-unit">{{
                                    $t('oneOfficeOneScreen.safety.units.items') }}</span></div>
                            </div> -->

                            <div class="hazard-item top-left">
                                <div class="hazard-label">{{
                                    $t('oneOfficeOneScreen.safety.hazardTypeAnalysis.siteGovernance') }}</div>
                                <div class="hazard-value">4<span class="hazard-unit">{{
                                    $t('oneOfficeOneScreen.safety.units.items') }}</span></div>
                            </div>

                            <div class="hazard-item top-right">
                                <div class="hazard-label">{{ $t('oneOfficeOneScreen.safety.hazardTypeAnalysis.hazard3')
                                }}</div>
                                <div class="hazard-value">1<span class="hazard-unit">{{
                                    $t('oneOfficeOneScreen.safety.units.items') }}</span></div>
                            </div>

                            <div class="hazard-item bottom-left">
                                <div class="hazard-label">{{ $t('oneOfficeOneScreen.safety.hazardTypeAnalysis.hazard2')
                                }}</div>
                                <div class="hazard-value">2<span class="hazard-unit">{{
                                    $t('oneOfficeOneScreen.safety.units.items') }}</span></div>
                            </div>

                            <div class="hazard-item bottom-right">
                                <div class="hazard-label">{{ $t('oneOfficeOneScreen.safety.hazardTypeAnalysis.hazard4')
                                }}</div>
                                <div class="hazard-value">2<span class="hazard-unit">{{
                                    $t('oneOfficeOneScreen.safety.units.items') }}</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 隐患问题top5 -->
                <div class="stat-card">
                    <div class="chart-header">
                        <span>{{ $t('oneOfficeOneScreen.safety.hazardTop5.title') }}</span>
                    </div>
                    <div class="chart-body">
                        <!-- 隐患问题top5列表 -->
                        <div class="top5-container">
                            <div class="top5-item" v-for="(item, index) in top5Data" :key="index"
                                :class="`top5-bg-${index + 1}`">
                                <div class="top5-content">
                                    <div class="top5-left">
                                        <span class="category-text">{{ item.name }}</span>
                                    </div>
                                    <div class="top5-right">
                                        <span class="label-text">{{
                                            $t('oneOfficeOneScreen.safety.hazardTop5.hazardTypeLabel') }}</span>
                                        <span class="number-value">{{ item.count }}</span>
                                        <span class="unit-text">{{ $t('oneOfficeOneScreen.safety.units.items') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import SafetyRingChart from '@/components/SafetyRingChart.vue';
import HazardDoughnutChart from '@/components/HazardDoughnutChart.vue';

const router = useRouter();
const { t } = useI18n();

// 3D环形图数据
const analysisData = [
    { name: '日常检查', value: 45, itemStyle: { color: '#0085FF' } },
    { name: '专项检查', value: 30, itemStyle: { color: '#87DBFF' } },
    { name: '突击检查', value: 25, itemStyle: { color: '#EEC01B' } }
];

// 隐患级别环形图数据
const hazardTypeData = [
    { name: '一级', value: 4 },
    { name: '三级', value: 2 },
    { name: '二级', value: 1 },
    { name: '四级', value: 2 }
];

// 隐患问题top5数据
const top5Data = [
    { name: '高处作业', count: 58 },
    { name: '文明施工', count: 20 },
    { name: '施工机具', count: 12 },
    { name: '脚手架', count: 2 },
    { name: '起重吊装', count: 2 }
];

// 表格数据
const tableData = [
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '阿联酋'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '伊拉克'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '伊拉克'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '阿联酋'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '津巴布韦'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '津巴布韦'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '津巴布韦'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '津巴布韦'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '津巴布韦'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '津巴布韦'
    },
    {
        checkTime: '2025-02-16',
        checkPoint: '定点1',
        checker: '人名人名',
        situation: '巡检情况巡检情况巡检情况',
        qrCode: '津巴布韦'
    }
];

const back = () => {
    router.push({ name: 'yibuyipingDetail' });
};

</script>

<style scoped lang="scss">
.anquan-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(135deg, #0a1628 0%, #1a2744 100%);
    overflow: hidden;
    font-family: 'Microsoft YaHei', sans-serif;
    pointer-events: all;
}

.main-content {
    margin-top: 130px;
    display: flex;
    gap: 20px;
    padding: 0 20px 3% 20px;
    height: calc(100% - 120px);
}

.left-section,
.right-section {
    flex: 0.8;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.center-section {
    flex: 2.4;
    display: flex;
    flex-direction: column;

}

.stat-card {
    background: rgba(5, 30, 60, 0.8);
    border: 1px solid rgba(0, 200, 255, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    flex: 1;
    min-height: 200px;

    &.center-box {
        height: 100%;
    }

    .project-analysis-header {
        background: url('@/assets/images/yibuyiping/project.png') no-repeat center/cover !important;
        background-size: 100% 100% !important;
        background-repeat: no-repeat !important;
        position: relative !important;
    }

    .chart-header {
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding-left: 50px;
        background: url('@/assets/images/zichan/box-title.png') no-repeat center/cover;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;

        span {
            font-family: "Alimama", sans-serif;
            font-weight: 700;
            font-size: 16px;
            background-image: linear-gradient(to bottom,
                    #006FD0 0%,
                    #FFFFFF 50%,
                    #006FD0 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
        }
    }

    .chart-body {
        width: 100%;
        height: calc(100% - 40px);
        padding: 15px 15px 10px 15px;
        position: relative;
        background: url('@/assets/images/zichan/box-content.png') no-repeat center/cover;
        background-size: cover;
        background-repeat: no-repeat;
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
    }
}

// 检查类型分析特殊样式
.analysis-top {
    height: 50%;
    width: 100%;
    background: url('@/assets/images/yibuyiping/safety-l-1.png') no-repeat center/cover;
    background-size: 70% 70%;
    background-position: 50% 70%;
}

.analysis-bottom {
    height: 50%;
    display: flex;
    gap: 23px;
    width: 96%;
    margin: 0 auto;
}

.mini-card {
    flex: 1;
    height: 100%;
    position: relative;

    &.card-1 {
        background: url('@/assets/images/yibuyiping/safety-l-2.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    &.card-2 {
        background: url('@/assets/images/yibuyiping/safety-l-3.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    &.card-3 {
        background: url('@/assets/images/yibuyiping/safety-l-4.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    .mini-content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 8px;

        .mini-upper {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;

            .mini-chart {
                color: rgba(255, 255, 255, 0.6);
                font-size: 10px;
                text-align: center;
                padding: 5px;
                border: 1px dashed rgba(64, 158, 255, 0.2);
                border-radius: 3px;
                background: rgba(64, 158, 255, 0.02);
                width: 100%;
            }
        }

        .mini-lower {
            height: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 2px;

            .mini-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #00BFFF;
                font-weight: 600;
            }

            .mini-label {
                padding-top: 20px;
                text-align: center;
                color: rgba(255, 255, 255, 0.6);
            }
        }
    }
}

.placeholder-content {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    text-align: center;
    padding: 40px 20px;
    border: 2px dashed rgba(64, 158, 255, 0.2);
    border-radius: 6px;
    background: rgba(64, 158, 255, 0.02);
}

// 项目检查分析样式
.project-analysis-container {
    width: 100%;
    height: 16%;
    display: flex;
    gap: 20px;
    padding: 15px 20px 4px 20px;
    background: url('@/assets/images/yibuyiping/safety-l-9.png') no-repeat center/cover;
    background-size: 100% 100%;
}

.project-item {
    flex: 1;
    height: 100%;
    position: relative;
    border-radius: 6px;
    overflow: hidden;

    &.item-1 {
        background: url('@/assets/images/yibuyiping/safety-l-6.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    &.item-2 {
        background: url('@/assets/images/yibuyiping/safety-l-7.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    &.item-3 {
        background: url('@/assets/images/yibuyiping/safety-l-8.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    .project-content {
        width: 84%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        text-align: center;

        .project-label {
            font-family: 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: -6px;
        }

        .project-value {
            padding-top: 10px;
            font-family: TCloudNumber, TCloudNumber;
            font-weight: bold;
            font-size: 32px;
            color: #00BFFF;
            text-shadow: 0 0 10px rgba(0, 191, 255, 0.5);

            .project-unit {
                font-family: 'Microsoft YaHei', sans-serif;
                font-weight: normal;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.7);
                margin-left: 3px;
                text-shadow: none;
            }
        }
    }
}

// 表格样式
.safety-table-container {
    flex: 1;
    height: calc(100% - 130px);
    padding: 20px 0px 20px 0px;
    overflow: hidden;
}

.table-wrapper {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    border-radius: 6px;
}

.safety-table {
    width: 100%;
    height: 100%;
    border-collapse: separate;
    border-spacing: 3px;
    color: #fff;
    font-family: 'Microsoft YaHei', sans-serif;

    thead {
        position: sticky;
        top: 0;
        z-index: 10;

        th {
            padding: 15px 10px;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #FFFFFF;
            background: rgba(11, 49, 89, 0.9);
            box-shadow: inset 0px 2px 4px 0px rgba(0, 92, 211, 0.05);
            border-radius: 0px 0px 0px 0px;
            border: 1px solid rgba(7, 64, 123, 0.5);
            border-image: linear-gradient(360deg, rgba(7, 64, 123, 0.3) 0%, rgba(0, 90, 173, 0.8) 100%) 1;
        }
    }

    tbody {
        tr {
            transition: all 0.3s ease;

            &:hover {
                background: rgba(0, 133, 255, 0.05);
                box-shadow: 0 0 10px rgba(0, 191, 255, 0.1);
            }

            td {
                padding: 12px 10px;
                text-align: center;
                font-size: 13px;
                color: #FFFFFF;
                line-height: 1.4;
                background: rgba(14, 42, 72, 0.6);
                box-shadow: inset 0px 2px 4px 0px rgba(0, 92, 211, 0.05);
                border-radius: 0px 0px 0px 0px;
                border: 1px solid rgba(7, 64, 123, 0.5);
                border-image: linear-gradient(360deg, rgba(7, 64, 123, 0.3) 0%, rgba(0, 90, 173, 0.8) 100%) 1;
            }
        }
    }
}

.action-btn {
    background: none;
    border: none;
    padding: 0;
    color: #79BFFF;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Microsoft YaHei', sans-serif;

    &:hover {
        color: #8DD0FF;
        text-shadow: 0 0 5px rgba(121, 191, 255, 0.5);
    }

    &:active {
        color: #5AAAFF;
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .anquan-container {
        padding: 60px 20px 20px 20px;
    }

    .main-content {
        gap: 15px;
    }

    .left-section,
    .right-section {
        gap: 15px;
    }

    .chart-header span {
        font-size: 14px;
    }

    .analysis-bottom {
        gap: 5px;
    }

    .mini-content {
        padding: 5px;
    }
}

// 自定义滚动条
.chart-body::-webkit-scrollbar {
    width: 6px;
}

.chart-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.chart-body::-webkit-scrollbar-thumb {
    background: rgba(64, 158, 255, 0.4);
    border-radius: 3px;

    &:hover {
        background: rgba(64, 158, 255, 0.6);
    }
}

// 表格滚动条样式
.table-wrapper::-webkit-scrollbar {
    width: 6px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: rgba(64, 158, 255, 0.4);
    border-radius: 3px;

    &:hover {
        background: rgba(64, 158, 255, 0.6);
    }
}

// 从detail.vue复制的企业检查样式
.renyuanTop {
    font-size: 14px;
    background: url('@/assets/images/yibuyiping/renyuanguanli-top.png') no-repeat center/cover;
    width: 100%;
    height: 43px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: left;
    padding-left: 100px;
    font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
    font-weight: bold;
    font-size: 18px;
    color: #FFFFFF;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 10%;
}

.renyuanTop-num {
    font-family: TCloudNumber, TCloudNumber;
    font-weight: bold;
    font-size: 28px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    background: linear-gradient(89.99deg, #E5F5FF 0%, #76B6FF 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 100px;
    display: block;

    .unit {
        font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
        font-weight: normal;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.5);
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
    }
}

.safety-stats-container {
    background: url('@/assets/images/yibuyiping/safety-l-5.png') no-repeat center/cover;
    background-size: 100% 100%;
    position: relative;
    width: 100%;
    height: 60%;
    margin-top: 30px;

    .corner-item {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;

        .label {
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }

        .value {
            font-family: TCloudNumber, TCloudNumber;
            font-weight: bold;
            font-size: 20px;
            color: #0085FF;

            .unit-text {
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
                margin-left: 2px;
            }
        }
    }

    .top-left {
        top: 50px;
        left: 35px;
    }

    .top-right {
        top: 50px;
        right: 45px;
    }

    .bottom-left {
        bottom: 39px;
        left: 35px;
    }

    .bottom-right {
        bottom: 36px;
        right: 45px;
    }
}

.hazard-type-container {
    width: 100%;
    height: 88%;
    position: relative;
    background: url('@/assets/images/yibuyiping/safety-l-10.png') no-repeat center/cover;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-top: 5%;

    .hazard-chart-wrapper {
        position: absolute;
        width: 180px;
        height: 180px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .hazard-item {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        z-index: 2;

        .hazard-label {
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }

        .hazard-value {
            font-family: TCloudNumber, TCloudNumber;
            font-weight: bold;
            font-size: 20px;
            color: #0085FF;

            .hazard-unit {
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
                margin-left: 2px;
            }
        }
    }

    .center-item {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 180px;
        height: 200px;

        .hazard-chart {
            width: 120px;
            height: 120px;
            margin: 10px auto 0;
        }
    }

    .top-left {
        top: 102px;
        left: 35px;
    }

    .top-right {
        top: 102px;
        right: 49px;
    }

    .bottom-left {
        bottom: 94px;
        left: 35px;
    }

    .bottom-right {
        bottom: 94px;
        right: 49px;
    }
}

.top5-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    height: 94%;
    padding-top: 30px;
}

.top5-item {
    flex: 1;
    width: 100%;
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    min-height: 45px;

    &.top5-bg-1 {
        background: url('@/assets/images/yibuyiping/safety-l-11.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    &.top5-bg-2 {
        background: url('@/assets/images/yibuyiping/safety-l-12.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    &.top5-bg-3 {
        background: url('@/assets/images/yibuyiping/safety-l-13.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    &.top5-bg-4 {
        background: url('@/assets/images/yibuyiping/safety-l-14.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    &.top5-bg-5 {
        background: url('@/assets/images/yibuyiping/safety-l-15.png') no-repeat center/cover;
        background-size: 100% 100%;
    }

    .top5-content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px 0px 52px;

        .top5-left {
            display: flex;
            align-items: center;
            gap: 5px;

            .category-text {
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
            }
        }

        .top5-right {
            display: flex;
            align-items: center;
            gap: 5px;

            .label-text {
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
            }

            .number-value {
                font-family: TCloudNumber, TCloudNumber;
                font-weight: bold;
                font-size: 24px;
                color: #00BFFF;
                text-shadow: 0 0 10px rgba(0, 191, 255, 0.5);
            }

            .unit-text {
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
            }
        }
    }
}
.back-button {
    position: absolute;
    top: 30px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
}
</style>