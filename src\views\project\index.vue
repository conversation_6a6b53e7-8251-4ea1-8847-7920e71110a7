<template>
  <div
    class="project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between">
    <!-- <div class="icon" v-if="!showBack">
      <img src="@/assets/images/layout/home.png" alt="" srcset="" />
    </div> -->
    <div class="leftList" v-if="!isShow">
      <div class="items" v-for="item in itemList" :key="item.id">
        <div class="item-img">
          <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
              style="width: 48px;height: 48px;" /></RotatingCircle>
        </div>
        <div class="item-content">
          <div class="item-title">{{ item.name }}</div>
          <div class="item-amount">
            <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
              <count-to v-if="item.id === 3 || item.id === 4 || item.id === 5" :start-val="0" :end-val="item.amount"
                :duration="2000" :decimals="0" :autoplay="true" separator="," />
              <template v-else>{{ formattedNumber(item.amount) }}</template>
            </span><span class="bgfont">{{ item.unit }}</span>
          </div>
          <div></div>
        </div>
        <div class="absolute right-9 top-20">
          <div v-if="item.id == '6'">
            <span
              style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;font-size: 18px;margin-right: 6px;">{{
                $t('project.chinese') }}</span>
            <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
              formattedNumber(foreignData?.中方 || 0)
            }}</span>{{ $t('project.people') }}
          </div>
          <div v-if="item.id == '6'">
            <span
              style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;font-size: 18px;margin-right: 6px;">{{
                $t('project.foreign') }}</span>
            <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
              formattedNumber(foreignData?.外籍 || 0)
            }}</span>{{ $t('project.people') }}
          </div>
        </div>
      </div>
    </div>
    <div class="rightList" v-if="isShow">
      <div class="items" v-for="item in itemListRight" :key="item.id">
        <div class="item-img">
          <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
              style="width: 48px;height: 48px;" /></RotatingCircle>
        </div>
        <div class="item-content">
          <div class="item-title">{{ item.name }}</div>
          <div class="item-amount">
            <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
              <count-to v-if="item.id === 3 || item.id === 4 || item.id === 5" :start-val="0"
                :end-val="parseAmount(item.amount)" :duration="2000" :decimals="0" :autoplay="true" separator="," />
              <template v-else>{{ formattedNumber(item.amount) }}</template>
            </span><span class="bgfont">{{ item.unit }}</span>
          </div>
          <div></div>
        </div>
        <div class="absolute right-3 top-20">
          <div v-if="item.id == '6'">
            <span
              style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;margin-right: 6px;">{{
                $t('project.formalEmployees') }}</span>
            <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
              formattedNumber(innerData?.正式员工 || 0)
            }}</span>{{ $t('project.people') }}
          </div>
          <div v-if="item.id == '6'">
            <span
              style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;margin-right: 6px;">{{
                $t('project.otherEmployment') }}</span>
            <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
              formattedNumber(innerData?.其他形式用工 || 0)
            }}</span>{{ $t('project.people') }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="showBack" class="back-button" @click="back">
            <img src="@/assets/images/header/back.png" alt="" />
            <span>返回</span>
        </div>
    <!-- <img v-if="showBack" class="pointer-events-auto cursor-pointer absolute top-[30px] left-[60px]"
      src="@/assets/images/xiangmu/back.png" alt="" srcset="" @click="back" /> -->
    <!-- <div class="left-container pointer-events-auto w-[442px] h-[922px] rotate-perspective">
            <div class="w-[442px] h-[922px] py-[25px] box-border rotate-item1">
                <div class="w-full text-center text-[22px] font-family-youshebiaotihei h-[33px] leading-[33px]">在建项目总览</div>
                <div class="w-full h-[100px] box-border flex flex-row flex-wrap">
                    <div class="w-1/2 h-[100px] flex items-end">
                        <img class="w-[111px] h-[100px]" src="@/assets/images/xiangmu/left-img1.png" alt="" srcset="">
                        <div class="ml-[6px]">
                            <div class="w-[150px] font-light text-[14px] mt-[-30px]">在建项目总数</div>
                            <div class="text-[14px]"><span class="text-[#a1fcd7] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">211</span>个</div>
                        </div>
                    </div>
                    <div class="w-1/2 h-[100px] flex items-end ml-[-26px]">
                        <img class="w-[111px] h-[100px]" src="@/assets/images/xiangmu/left-img2.png" alt="" srcset="">
                        <div class="ml-[6px]">
                            <div class="w-[150px] font-light text-[14px] mt-[-30px]">在建项目合同总额</div>
                            <div class="text-[14px]"><span class="text-[#a1fcd7] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">934,084</span>万元</div>
                        </div>
                    </div>
                </div>
                <div class="w-full h-[200px] box-border flex flex-row flex-wrap">
                    <div class="w-[220px] h-[200px] flex justify-center items-center">
                        <Pie3DChart2 
                            :data="pieData" 
                            width="220px"
                            height="220px"
                        />
                    </div>
                    <div class="w-[220px] h-[200px] flex justify-center items-center">
                        <Pie3DChart2 
                            :data="pieData2" 
                            width="220px"
                            height="220px"
                        />
                    </div>
                </div>
                <div class="flex items-end h-[150px] justify-evenly mt-[50px]">
                    <div class="flex flex-col items-center">
                        <div class="text-[14px]"><span class="text-[#a1fcd7] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">441,150</span>万元</div>
                        <img src="@/assets/images/xiangmu/left-img3.png" alt="" srcset="">
                        <div class="items-center font-light text-[16px] mt-[-30px]">工程款收入</div>
                    </div>
                    <div class="w-[240px]">
                        <div class="w-[240px] h-[21px] bg-[url('@/assets/images/xiangmu/left-img4.png')] bg-no-repeat bg-cover flex justify-between items-center">
                            <span class="text-[12px] font-light ml-[26px]">本年预算</span>
                            <div class="text-[14px] mt-[-10px]"><span class="text-[#a1fcd7] text-[20px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">500,000</span>万元</div>
                        </div>
                        <div class="w-[240px] h-[20px] flex justify-between items-center mt-[14px]">
                            <div class="">完成度</div>
                            <div class="text-[14px] text-[#a1fcd7] leading-[50px] font-family-oswald-medium tracking-[1.3px]"><span class="text-[18px]">88.23</span><span>%</span></div>
                        </div>
                        <div class="relative w-[240px] h-[8px] bg-[#fff]/[0.1] rounded-[4px] mt-[10px]">
                            <div class="jindu-container-green absolute top-0 left-0 w-[211px] h-[8px] jindu-jianbian-green rounded-[4px] "></div>
                        </div>
                        <div class="w-[240px] h-[20px] flex justify-between items-center mt-[14px]">
                            <div class="">同比增长</div>
                            <div class="text-[14px] text-[#a1fcd7] leading-[50px] font-family-oswald-medium tracking-[1.3px]"><span class="text-[18px]">+21.44</span><span>%</span></div>
                        </div>
                        <div class="relative w-[240px] h-[8px] bg-[#fff]/[0.1] rounded-[4px] mt-[10px]">
                            <div class="jindu-container-green absolute top-0 left-0 w-[51px] h-[8px] rounded-[4px]"></div>
                        </div>
                    </div>
                    
                </div>  
                <div class="w-full h-[300px] flex justify-center items-center ml-[10px] mt-[50px]">
                    <StaffChart2 title="人员总数" total="14664" :data="pieChartData"/>
                </div>
            </div>
        </div>
        <div class="right-container pointer-events-auto w-[442px] h-[922px] rotate-perspective">
            <div class="w-[442px] h-[922px] py-[25px] box-border rotate-item2">
                <div class="w-full text-center text-[22px] font-family-youshebiaotihei h-[33px] leading-[33px]">在建项目安全质量检查</div>
                <div class="w-full h-[100px] box-border flex flex-row flex-wrap">
                    <div class="w-1/2 h-[100px] flex items-end">
                        <img class="w-[111px] h-[100px]" src="@/assets/images/xiangmu/right-img1.png" alt="" srcset="">
                        <div class="ml-[6px]">
                            <div class="w-[150px] font-light text-[14px] mt-[-30px]">排查记录</div>
                            <div class="text-[14px]"><span class="text-[#a1fcd7] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">876</span></div>
                        </div>
                    </div>
                    <div class="w-1/2 h-[100px] flex items-end ml-[-26px]">
                        <img class="w-[111px] h-[100px]" src="@/assets/images/xiangmu/right-img2.png" alt="" srcset="">
                        <div class="ml-[6px]">
                            <div class="w-[150px] font-light text-[14px] mt-[-30px]">隐患记录</div>
                            <div class="text-[14px]"><span class="text-[#da3832] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">36,059</span></div>
                        </div>
                    </div>
                </div>
                <div class="w-full h-[300px] flex justify-center items-center ml-[10px]">
                    <StaffChart2 title="质量问题总数" total="211" :data="pieChart2Data"/>
                </div>
                <div class="w-full h-[120px] flex justify-evenly items-center ml-[10px]">
                    <div class="flex items-center">
                        <div class="w-[120px] h-[120px] bg-[url('@/assets/images/xiangmu/waterball-bg1.png')] bg-no-repeat bg-cover flex justify-center items-center">
                            <WaterBall width="110px" height="110px" value="0.89" waveColor="#6deaa5"/>
                        </div>
                        <div class="ml-[10px]">
                            <div class="text-[14px]"><span class="text-[26px] text-[#a1fcd7] leading-[50px] font-family-oswald-medium tracking-[1.3px]">89</span><span>%</span></div>
                            <div class="text-[14px] font-light">整改率</div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="w-[120px] h-[120px] bg-[url('@/assets/images/xiangmu/waterball-bg2.png')] bg-no-repeat bg-cover flex justify-center items-center">
                            <WaterBall width="110px" height="110px" value="0.53" waveColor="#f0b354"/>
                        </div>
                        <div class="ml-[10px]">
                            <div class="text-[14px]"><span class="text-[26px] text-[#f0b354] leading-[50px] font-family-oswald-medium tracking-[1.3px]">53</span><span>%</span></div>
                            <div class="text-[14px] font-light">整改及时率</div>
                        </div>
                    </div>
                </div>
                <div class="relative w-[400px] h-[300px] flex justify-center items-center ml-[10px]">
                    <div class="absolute top-[10px] left-0 w-[391px] h-[27px] bg-[url('@/assets/images/zichan/title-bg.png')] bg-no-repeat bg-cover text-[14px] font-family-youshebiaotihei leading-[30px] pl-[30px] box-border">年质量问题及整改趋势</div>
                    <LineChart :data="chartDate" :legend="legend" :xData="xData" width="100%" height="300px" />
                </div>
            </div>
        </div> -->
    <div class="rightList" v-if="!isShow">
      <div class="items" v-for="item in itemListRight" :key="item.id">
        <div class="item-img">
          <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
              style="width: 48px;height: 48px;" /></RotatingCircle>
        </div>
        <div class="item-content">
          <div class="item-title">{{ item.name }}</div>
          <div class="item-amount">
            <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
              <count-to v-if="item.id === 3 || item.id === 4 || item.id === 5" :start-val="0"
                :end-val="parseAmount(item.amount)" :duration="2000" :decimals="0" :autoplay="true" separator="," />
              <template v-else>{{ formattedNumber(item.amount) }}</template>
            </span><span class="bgfont">{{ item.unit }}</span>
          </div>
          <div></div>
        </div>
        <div class="absolute right-3 top-20">
          <div v-if="item.id == '6'">
            <span
              style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;margin-right: 6px;">{{
                $t('project.formalEmployees') }}</span>
            <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
              formattedNumber(innerData?.正式员工 || 0) }}</span>{{ $t('project.people') }}
          </div>
          <div v-if="item.id == '6'">
            <span
              style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;margin-right: 6px;">{{
                $t('project.otherEmployment') }}</span>
            <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
              formattedNumber(innerData?.其他形式用工 || 0) }}</span>{{ $t('project.people') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { CountTo } from 'vue3-count-to';
import RotatingCircle from '@/components/RotatingCircle.vue';
import { onMounted, ref, onUnmounted, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useI18n } from 'vue-i18n';
import country from "@/assets/images/layout/country.png";
import project from "@/assets/images/layout/project.png";
import amount from "@/assets/images/layout/amount.png";
import produce from "@/assets/images/layout/produce.png";
import engineering from "@/assets/images/layout/engineering.png";
import people from "@/assets/images/layout/people.png";
import request from '@/utils/request';

const router = useRouter();
const route = useRoute();
const { t, locale } = useI18n();

const iframe = ref(document.getElementById("iframe"));

const showBack = ref(false);

const isShow = ref(false);

const foreignData = ref(null);
const innerData = ref(null);

// 境外数据列表 - 计算属性
const itemList = computed(() => {
  if (!foreignData.value) return [];
  return [
    {
      id: 1,
      src: country,
      name: t('project.dataFields.overseasBusinessCountries'),
      amount: Number(foreignData.value.境外业务布局国家 || 0),
      unit: t('project.units.project'),
      svgName: "home-1"
    },
    {
      id: 2,
      src: project,
      name: t('project.dataFields.overseasProjects'),
      amount: Number(foreignData.value.境外在建项目 || 0),
      unit: t('project.units.project'),
      svgName: "home-2"
    },
    {
      id: 3,
      src: amount,
      name: t('project.dataFields.contractAmount'),
      amount: Number(foreignData.value.合同总额 || 0),
      unit: t('project.units.hundredMillionUSD'),
      svgName: "home-3-l"
    },
    // {
    //   id: 4,
    //   src: produce,
    //   name: t('project.dataFields.cumulativeOutput'),
    //   amount: Number(foreignData.value.累计产值 || 0),
    //   unit: t('project.units.tenThousandUSD'),
    //   svgName: "home-4-l"
    // },
    // {
    //   id: 5,
    //   src: engineering,
    //   name: t('project.dataFields.cumulativeEngineering'),
    //   amount: Number(foreignData.value.累收工程款 || 0),
    //   unit: t('project.units.tenThousandUSD'),
    //   svgName: "home-5-l"
    // },
    {
      id: 6,
      src: people,
      name: t('project.dataFields.overseasPersonnel'),
      amount: Number(foreignData.value.项目总人数 || 0),
      unit: t('project.units.person'),
      svgName: "home-6"
    }
  ];
});

// 境内数据列表 - 计算属性
const itemListRight = computed(() => {
  if (!innerData.value) return [];
  return [
    {
      id: 1,
      src: country,
      name: t('project.dataFields.domesticBusinessProvinces'),
      amount: Number(innerData.value.境内业务省份 || 0),
      unit: t('project.units.project'),
      svgName: "home-1"
    },
    {
      id: 2,
      src: project,
      name: t('project.dataFields.domesticProjectsWithDesign'),
      amount: Number(innerData.value.国内在建项目 || 0),
      unit: t('project.units.project'),
      svgName: "home-2"
    },
    {
      id: 3,
      src: amount,
      name: t('project.dataFields.contractAmount'),
      amount: parseAmount(innerData.value.合同总额 || 0),
      unit: t('project.units.hundredMillionYuan'),
      svgName: "home-3-r"
    },
    {
      id: 4,
      src: produce,
      name: t('project.dataFields.currentYearCollectionRate'),
      amount: innerData.value.本年回款率 ? (Number(innerData.value.本年回款率) * 100).toFixed(2) : '0.00',
      unit: '%',
      svgName: "home-4-r"
    },
    // {
    //   id: 5,
    //   src: engineering,
    //   name: t('project.dataFields.cumulativeEngineering'),
    //   amount: innerData.value.累收工程款 || innerData.value.工程款收入 || 0,
    //   unit: t('project.units.tenThousandYuan'),
    //   svgName: "home-5-r"
    // }
  ];
});
const formattedNumber = (value) => {
  if (typeof value == "number") {
    return value.toLocaleString();
  } else {
    return value;
  }
};

const pieChartData = computed(() => [
  {
    value: 2482,
    name: t('project.staffTypes.formalEmployees'),
    itemStyle: { color: "rgba(0, 255, 187, 1)" },
  },
  {
    value: 5968,
    name: t('project.staffTypes.overseasLocalStaff'),
    itemStyle: { color: "rgba(255, 159, 67, 1)" },
  },
  {
    value: 4168,
    name: t('project.staffTypes.laborDispatch'),
    itemStyle: { color: "rgba(224, 224, 224, 1)" },
  },
  {
    value: 1073,
    name: t('project.staffTypes.otherEmploymentForms'),
    itemStyle: { color: "rgba(74, 144, 226, 1)" },
  },
  {
    value: 973,
    name: t('project.staffTypes.partnerStaff'),
    itemStyle: { color: "rgba(0, 216, 255, 1)" },
  },
]);

const pieChart2Data = computed(() => [
  {
    value: 56,
    name: t('project.qualityStatus.pendingRectification'),
    itemStyle: { color: "rgba(217, 59, 53, 1)" },
  },
  {
    value: 79,
    name: t('project.qualityStatus.qualified'),
    itemStyle: { color: "rgba(0, 255, 187, 1)" },
  },
  {
    value: 27,
    name: t('project.qualityStatus.pendingVerification'),
    itemStyle: { color: "rgba(0, 216, 255, 1)" },
  },
  {
    value: 49,
    name: t('project.qualityStatus.rectified'),
    itemStyle: { color: "rgba(255, 159, 67, 1)" },
  },
]);

const legend = computed(() => [t('project.chartItems.problemCount'), t('project.chartItems.rectificationCount')]);
const xData = computed(() => [
  t('project.months.march'),
  t('project.months.april'),
  t('project.months.may'),
  t('project.months.june'),
  t('project.months.july'),
  t('project.months.august'),
  t('project.months.september'),
  t('project.months.october'),
  t('project.months.november'),
  t('project.months.december'),
  t('project.months.january'),
  t('project.months.february'),
]);
const chartDate = computed(() => [
  {
    name: t('project.chartItems.problemCount'),
    itemStyle: { color: "#74fbfd" },
    data: [180, 120, 110, 150, 170, 240, 250, 350, 360, 320, 280, 180],
  },
  {
    name: t('project.chartItems.rectificationCount'),
    itemStyle: { color: "#d93b35" },
    data: [130, 100, 120, 140, 160, 220, 230, 280, 280, 260, 240, 180],
  },
]);

const pieData = computed(() => [
  {
    value: 127,
    name: t('project.chartItems.domestic'),
    unit: t('project.units.project'),
    itemStyle: { color: "rgba(85,184,242, 1)" },
  },
  {
    value: 14,
    name: t('project.chartItems.international'),
    unit: t('project.units.project'),
    itemStyle: { color: "rgba(114,273,176, 1)" },
  },
]);

const pieData2 = computed(() => [
  {
    value: 876420,
    name: t('project.chartItems.domestic'),
    unit: t('project.units.tenThousandYuan'),
    itemStyle: { color: "rgba(85,184,242, 1)" },
  },
  {
    value: 57664,
    name: t('project.chartItems.international'),
    unit: t('project.units.tenThousandYuan'),
    itemStyle: { color: "rgba(114,273,176, 1)" },
  },
]);
let backTimers = null;
const back = () => {
  if (backTimers) {
    clearTimeout(backTimers); // 如果已经设置了延迟，则清除之前的定时器
  }
  localStorage.removeItem("isChina");
  sessionStorage.removeItem("lastProjectState");
  // 确保 iframe 存在后再发送消息
  backTimers = setTimeout(() => {
    iframe.value.contentWindow.postMessage(
      { eve: "cancle" },
      "*"
    );
    backTimers = null;
  }, 900);
  showBack.value = false;
  isShow.value = false;
};

const parseAmount = (value) => {
  if (typeof value === 'string') {
    return Number(value.replace(/,/g, ''));
  }
  return value;
};

const getForeignInfo = async () => {
  try {
    const response = await request.get('/globalManage/zjmanage/largescreen/getForeignInfo');
    if (response.code === 0 && response.data.length > 0) {
      foreignData.value = response.data[0];
      console.log('境外数据', foreignData.value)
    }
  } catch (error) {
    console.error('获取境外数据失败:', error);
  }
};

const getInnerInfo = async () => {
  try {
    const response = await request.get('/globalManage/zjmanage/largescreen/getInnerInfo', {
      params: { gb: '中国' }
    });
    console.log('中国')
    if (response.code === 0 && response.data.length > 0) {
      innerData.value = response.data[0];
    }
  } catch (error) {
    console.error('获取境内数据失败:', error);
  }
};

// 创建事件处理函数的引用，方便后续移除
const handleMessage = (e) => {
  console.log('完整的 e.data:', e.data)
  iframe.value.contentWindow.postMessage(
    {
      type: "token",
      data: sessionStorage.getItem('token'),
    },
    "*"
  );
  if (e.data.title && e.data.title !== "中国" && e.data.title !== "earth") {
    console.log('进入判断的 e.data:', e.data)
    // 使用sessionStorage存储数据而不是URL参数
    sessionStorage.setItem("countryLevel", e.data.level || '');
    sessionStorage.setItem("countryTitle", e.data.title);
    router.push({
      path: "/project/country"
    });
  }
  if (e.data.title === "中国") {
    // 设置延迟显示返回按钮
    setTimeout(() => {
      showBack.value = true;
    }, 2000);
    isShow.value = true;
    localStorage.setItem("isChina", "1");
    sessionStorage.setItem("countryTitle", "中国");
    // 添加当前路由状态到sessionStorage
    sessionStorage.setItem("lastProjectState", JSON.stringify({
      isChina: true,
      showBack: true,
      isShow: true
    }));
  }
  if (e.data.title === "earth") {
    router.push({
      path: "/project/index",
    });
    localStorage.removeItem("isChina");
    sessionStorage.removeItem("lastProjectState");
    // 确保返回地球时显示左右两边的数据
    showBack.value = false;
    isShow.value = false;
  }
  if (e.data.eve === "loadOk") {
    console.log('发送token')
    showBack.value = false;
    isShow.value = false;
    localStorage.removeItem("isChina");
    sessionStorage.removeItem("lastProjectState");
    iframe.value.contentWindow.postMessage(
      {
        type: "token",
        data: sessionStorage.getItem('token'),
      },
      "*"
    );
  }
  // 处理地图模式切换消息
  if (e.data.eve === "earth") {
    console.log('切换到地球模式')
    showBack.value = false;
    isShow.value = false;
    localStorage.removeItem("isChina");
    sessionStorage.removeItem("lastProjectState");
  }
};

// 监听路由变化，确保从其他页面返回时状态正确
watch(() => route.path, (newPath) => {
  if (newPath === '/project/index') {
    // 检查localStorage和sessionStorage中的状态
    const isChina = localStorage.getItem("isChina");
    const savedState = sessionStorage.getItem("lastProjectState");
    
    if (!isChina && !savedState) {
      // 如果没有中国状态，显示地球模式
      console.log('路由变化：重置为地球模式')
      showBack.value = false;
      isShow.value = false;
    }
  }
}, { immediate: true });

onMounted(async () => {
  iframe.value = document.getElementById("iframe");
  // 使用引用的事件处理函数
  window.addEventListener("message", handleMessage);

  // 恢复之前保存的状态
  const savedState = sessionStorage.getItem("lastProjectState");
  if (savedState) {
    try {
      const state = JSON.parse(savedState);
      if (state.isChina) {
        isShow.value = state.isShow;
        // 不立即设置showBack，而是延迟2秒
        setTimeout(() => {
          showBack.value = state.showBack;
        }, 2000);
      } else {
        // 如果状态中不是中国模式，确保显示地球模式
        showBack.value = false;
        isShow.value = false;
      }
    } catch (e) {
      console.error("恢复状态失败:", e);
      // 出错时默认显示地球模式
      showBack.value = false;
      isShow.value = false;
    }
  } else {
    // 没有保存状态时，默认显示地球模式
    showBack.value = false;
    isShow.value = false;
  }

  iframe.value.contentWindow.postMessage(
    {
      type: "token",
      data: sessionStorage.getItem('token'),
    },
    "*"
  );
    iframe.value.contentWindow.postMessage(
    {eve:"isPC",data:true},
    "*"
  );
 
  // 获取数据
  await getForeignInfo();
  await getInnerInfo();
});

// 添加组件销毁时的清理逻辑
onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener("message", handleMessage);
});

const routeData = route.query.data ? JSON.parse(route.query.data) : null;
</script>
<style scoped lang="scss">
.project-container {
  pointer-events: none;
  // background: url("@/assets/images/xiangmu/wangge.png") no-repeat center center / 1508px 961px;
  z-index: 2;

  .icon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: absolute;
    top: 70px;
    left: -20px;
    z-index: 99;
    // width: 180px;
    // height: 150px;
    // position: absolute;
    // top: 65px;
    // left: 30px;
    // display: flex;
    // flex-direction: column;
    // font-family: "宋体";
    // background-color: alpha($color: #1e2930);
    // .num {
    //   color: #00ffaa;
    //   font-size: 25px;
    //   font-weight: 900;
    //   span {
    //     font-weight: 300;
    //   }
    // }
  }

  .leftList {
    display: flex;
    flex-direction: column;
    position: relative;
    /* gap: 20px; */
    justify-content: space-around;
    height: 80%;
    top: 40px;
    z-index: 99;
    margin-left: 30px;

    .item-img {
      position: relative;
      top: -16px;
    }

    .items {
      display: flex;
      gap: 20px;
      position: relative;
      /* margin-bottom: 20px; */

      .item-amount {
        font-family: "xiaoweiLogo";
      }
    }
  }

  .rightList {
    display: flex;
    flex-direction: column;
    position: relative;
    /* gap: 57px; */
    justify-content: space-around;
    height: 80%;
    top: 60px;
    z-index: 99;
    margin-left: 40px;
    padding: 0 20px;

    .item-img {
      position: relative;
      top: -16px;
    }

    .items {
      display: flex;
      gap: 20px;
      position: relative;
      /* margin-bottom: 20px; */

      .item-amount {
        font-family: "xiaoweiLogo";
      }
    }
  }

  .left-container {
    background: url("@/assets/images/xiangmu/bg-left.png") no-repeat center center / 100% 100%,
      url("@/assets/images/xiangmu/left-bg.png") no-repeat center center / 100% 100%;
  }

  .right-container {
    background: url("@/assets/images/xiangmu/bg-right.png") no-repeat center center / 100% 100%,
      url("@/assets/images/xiangmu/right-bg.png") no-repeat center center / 100% 100%;
  }

  .rotate-perspective {
    perspective: 800px;

    .rotate-item1 {
      transform: rotate3d(0, 1, 0, 5.5deg);
    }

    .rotate-item2 {
      transform: rotate3d(0, 1, 0, -5.5deg);
    }
  }
}

.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/images/textures/bg.png") no-repeat center center / cover;
}

.bgfont {
  font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}
.back-button {
    position: absolute;
    top: 30px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
}
</style>
