import axios from "axios"
import { ElMessage, ElMessageBox } from "element-plus"

const request = axios.create({
  // 修改为实际的后端地址
  baseURL: window.baseEnv.baseURL,
  timeout: 1000 * 60 * 5, // request timeout
  withCredentials: true, // 启用 Cookie
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json, text/plain, */*',
    'tenant-id': '1',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  }
})

// 是否显示重新登录
export const isRelogin = { show: false }
// 请求队列
let requestList = []
// 是否正在刷新中
let isRefreshToken = false
// 请求白名单，无须token的接口
const whiteList = ['/login', '/refresh-token']

// 设置定时器，定期检查token是否即将过期
let tokenCheckTimer = null

// 启动token检查定时器
const startTokenCheck = () => {
  // 清除可能存在的旧定时器
  if (tokenCheckTimer) {
    clearInterval(tokenCheckTimer)
  }

  // 每5分钟检查一次token
  tokenCheckTimer = setInterval(async () => {
    const token = sessionStorage.getItem('token')
    const refreshToken = sessionStorage.getItem('refreshToken')

    if (token && refreshToken) {
      try {
        // 尝试刷新token
        await refreshTokenFn()
      } catch (error) {
        console.error('Token refresh failed:', error)
      }
    }
  }, 5 * 60 * 1000) // 5分钟
}

// 登录成功后调用此函数
export const setupTokenRefresh = (token, refreshToken) => {
  // 保存token和refreshToken
  sessionStorage.setItem('token', token)
  sessionStorage.setItem('refreshToken', refreshToken)

  // 启动token检查
  startTokenCheck()
}

// 登出时调用此函数
export const clearTokenRefresh = () => {
  // 清除token和refreshToken
  sessionStorage.removeItem('token')
  sessionStorage.removeItem('refreshToken')

  // 清除定时器
  if (tokenCheckTimer) {
    clearInterval(tokenCheckTimer)
    tokenCheckTimer = null
  }

  // 清除所有请求队列
  requestList = []
  isRefreshToken = false
}

// request interceptor
request.interceptors.request.use(
  (config) => {
    // 对于业务展示页面，添加特殊标记
    const currentPath = window.location.hash.split('?')[0]
    const pathname = window.location.pathname
    const href = window.location.href

    // 展示类与免登录入口页面的放行识别（用于弱化鉴权拦截影响）
    const isBusinessDisplay =
      // 业务展示与视频展示
      currentPath.includes('/business-display') ||
      currentPath.includes('/video-display') ||
      pathname.includes('/business-display') ||
      pathname.includes('/video-display') ||
      href.includes('/business-display') ||
      href.includes('/video-display') ||
      href.includes('#/business-display') ||
      href.includes('#/video-display') ||
      // 一部一屏系列页面（免登录入口跳转的主要落点）
      currentPath.includes('/yibuyiping') ||
      pathname.includes('/yibuyiping') ||
      href.includes('/yibuyiping') ||
      href.includes('#/yibuyiping') ||
      // 免登录触发页与快捷入口（auto- 前缀）
      currentPath.includes('/noAuth') ||
      pathname.includes('/noAuth') ||
      href.includes('/noAuth') ||
      href.includes('#/noAuth') ||
      currentPath.includes('/auto-') ||
      pathname.includes('/auto-') ||
      href.includes('/auto-') ||
      href.includes('#/auto-');

    if (isBusinessDisplay) {
      console.log('业务展示页面API请求，添加特殊标记:', config.url);
      config.headers['x-business-display'] = 'true';
    }

    // 从 sessionStorage 获取 token
    const token = sessionStorage.getItem('token')
    const refreshToken = sessionStorage.getItem('refreshToken')

    // 是否需要设置 token
    let isToken = (config.headers || {}).isToken === false
    whiteList.some((v) => {
      if (config.url) {
        config.url.indexOf(v) > -1
        return (isToken = false)
      }
    })

    if (token && !isToken) {
      config.headers["Authorization"] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// response interceptor
request.interceptors.response.use(
  async (response) => {
    const res = response.data
    const config = response.config
    console.log(res)
    // 如果返回的状态码是401，检查当前页面类型后处理
    if (res.code === 401) {
      return handleAuthorized()
    } else if (res.code !== 0) {
      // 对于业务展示页面，降低错误提示的严重性
      const currentPath = window.location.hash.split('?')[0]
      if (currentPath.includes('/business-display') || currentPath.includes('/video-display') ||
        window.location.pathname.includes('/business-display') || window.location.pathname.includes('/video-display')) {
        console.warn('业务展示页面API请求失败，但不影响页面访问:', res.msg || '请求失败')
        // 返回一个默认的空数据结果，不阻断页面渲染
        return { code: 0, data: null, msg: '业务展示页面数据加载失败，使用默认数据' }
      }
      ElMessage.error(res.msg || '请求失败')
      return Promise.reject(new Error(res.msg || '请求失败'))
    }

    return res
  },
  (error) => {
    console.error('响应错误:', error)
    // 处理401错误（需要从error.response中获取)
    if (error.response && error.response.status === 401) {
      return handleAuthorized()
    }

    // 对于业务展示页面，降低网络错误的影响
    const currentPath = window.location.hash.split('?')[0]
    if (currentPath.includes('/business-display') || currentPath.includes('/video-display') ||
      window.location.pathname.includes('/business-display') || window.location.pathname.includes('/video-display')) {
      console.warn('业务展示页面网络请求失败，但不影响页面访问:', error.message)
      // 返回一个默认的空数据结果，不阻断页面渲染
      return Promise.resolve({ code: 0, data: null, msg: '网络请求失败，使用默认数据' })
    }

    ElMessage.error(error.message || '请求失败')
    return Promise.reject(error)
  }
)

// 刷新token的函数
const refreshTokenFn = async () => {
  try {
    return await axios.post('/admin-api/system/auth/refresh-token', {
      refreshToken: sessionStorage.getItem('refreshToken')
    }, {
      headers: {
        'Content-Type': 'application/json',
        'tenant-id': '1'
      }
    })
  } catch (error) {
    handleAuthorized() // 刷新失败时调用未授权处理
    return Promise.reject(error)
  }
}
// 处理未授权的情况
const handleAuthorized = () => {
  if (!isRelogin.show) {
    // 判断当前路由是否是登录页面或业务展示页面
    const currentPath = window.location.hash.split('?')[0]
    const pathname = window.location.pathname
    const href = window.location.href

    // 添加调试信息
    console.log('handleAuthorized调试信息:', {
      currentPath,
      pathname,
      href,
      fullHash: window.location.hash
    });

    // 登录页面直接返回，不处理
    if (currentPath === '#/login' || currentPath === '/login' || pathname.includes('/login')) {
      console.log('检测到登录页面，不处理');
      return
    }

    // 业务展示相关页面，静默处理，不跳转登录页
    // 使用多种方式检查，确保在各种情况下都能正确识别
    const isBusinessDisplay =
      // 业务展示与视频展示
      currentPath.includes('/business-display') ||
      currentPath.includes('/video-display') ||
      pathname.includes('/business-display') ||
      pathname.includes('/video-display') ||
      href.includes('/business-display') ||
      href.includes('/video-display') ||
      href.includes('#/business-display') ||
      href.includes('#/video-display') ||
      // 一部一屏系列页面（免登录入口跳转的主要落点）
      currentPath.includes('/yibuyiping') ||
      pathname.includes('/yibuyiping') ||
      href.includes('/yibuyiping') ||
      href.includes('#/yibuyiping') ||
      // 免登录触发页与快捷入口（auto- 前缀）
      currentPath.includes('/noAuth') ||
      pathname.includes('/noAuth') ||
      href.includes('/noAuth') ||
      href.includes('#/noAuth') ||
      currentPath.includes('/auto-') ||
      pathname.includes('/auto-') ||
      href.includes('/auto-') ||
      href.includes('#/auto-');

    console.log('业务展示页面检查结果:', isBusinessDisplay);

    if (isBusinessDisplay) {
      console.warn('业务展示页面访问时token验证失败，但允许继续访问', {
        currentPath, pathname, href, isBusinessDisplay
      });
      return Promise.resolve({ code: 0, data: null, msg: '业务展示页面允许匿名访问' })
    }

    console.log('不是业务展示页面，将显示登录提示');

    isRelogin.show = true
    ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
      showCancelButton: false,
      closeOnClickModal: false,
      showClose: false,
      confirmButtonText: '重新登录',
      type: 'warning'
    }).then(() => {
      // 清理token
      clearTokenRefresh()
      isRelogin.show = false
      // 跳转到登录页
      window.location.href = '/login?redirect=' + encodeURIComponent(window.location.href)
    })
  }
  return Promise.reject('登录状态已过期')
}

export default request