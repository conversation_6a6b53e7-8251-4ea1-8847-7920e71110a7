import { onMounted, onUnmounted, ref } from "vue";

// * 设计稿尺寸（px）
const baseWidth = 5280;
const baseHeight = 1620;

/**
 * WindowScale适配Hook - 响应式样式方式
 * @returns {Object} 包含style响应式对象和相关方法
 */
export function useIndex() {
  const style = ref({
    width: `${baseWidth}px`,
    height: `${baseHeight}px`,
    transform: "translate(-50%, -50%) scale(1)", // 默认不缩放，垂直水平居中
  });

  // 获取缩放比例
  const getScale = () => {
    const w = window.innerWidth / baseWidth;
    const h = window.innerHeight / baseHeight;
    return w < h ? w : h;
  };

  // 设置缩放比例
  const setScale = () => {
    const scale = getScale();
    style.value.transform = `translate(-50%, -50%) scale(${scale})`;
    console.log(`WindowScale: 设置缩放比例 ${scale}, 元素尺寸 ${baseWidth}x${baseHeight}`);
  };

  // 防抖函数
  const debounce = (fn, t) => {
    const delay = t || 500;
    let timer;
    // eslint-disable-next-line func-names
    return function () {
      // eslint-disable-next-line prefer-rest-params
      const args = arguments;
      if (timer) {
        clearTimeout(timer);
      }
      const context = this;
      timer = setTimeout(() => {
        timer = null;
        fn.apply(context, args);
      }, delay);
    };
  };

  const resize = debounce(() => setScale(), 100);

  // 改变窗口大小重新绘制
  const windowDraw = () => {
    window.addEventListener("resize", resize);
  };

  // Vue生命周期管理
  onMounted(() => {
    setScale(); // 初始化时设置缩放
    windowDraw();
  });

  onUnmounted(() => {
    window.removeEventListener("resize", resize);
  });

  return { style, setScale, getScale };
}
