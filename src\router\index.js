import { createRouter, createWebHashHistory } from "vue-router";
import Layout from "@/layout";

const routes = [
  { path: "/", redirect: { path: "/login" } },
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/login/index.vue"),
  },
  // 业务布局展示页面路由组 - 独立全屏页面，不使用Layout包装，包含国家详情和项目详情子路由
  // 禁用keep-alive缓存以避免路由跳转问题
  {
    path: "/business-display",
    name: "businessDisplay",
    component: () => import("../views/business-display/index.vue"),
    meta: { keepAlive: false },
    children: [
      {
        path: "country",
        name: "businessDisplayCountry",
        component: () => import("../views/business-display/country.vue"),
        meta: { keepAlive: false, hideParent: true }
      },
      {
        path: "project-detail",
        name: "businessDisplayProjectDetail",
        component: () => import("../views/business-display/projectDetail.vue"),
        meta: { keepAlive: false, hideParent: true }
      },
      {
        path: "yingdi",
        name: "businessDisplayYingdi",
        component: () => import("../views/business-display/yingdi.vue"),
        meta: { keepAlive: false, hideParent: true }
      }
    ]
  },
  // 新增：视频展示页面路由（现场视频的副本，用于业务布局展示页面跳转）
  // 独立全屏页面，不使用Layout包装
  {
    path: "/video-display",
    name: "videoDisplayIndex",
    component: () => import("../views/video-display/index.vue"),
  },
  {
    path: "/home",
    name: "home",
    component: Layout,
    children: [
      {
        path: "index",
        name: "homeIndex",
        component: () => import("../views/home/<USER>"),
      },
    ],
  },
  {
    path: "/project",
    name: "project",
    component: Layout,
    children: [
      {
        path: "index",
        name: "projectIndex",
        component: () => import("../views/project/index.vue"),
      },
      // {
      //   path: "country",
      //   name: "countryIndex",
      //   component: () => import("../views/project/country.vue"),
      // },
      {
        path: "country",
        name: "countryIndex",
        component: () => import("../views/project/country2.vue"),
      },
    ],
  },
  {
    path: "/business",
    name: "business",
    component: Layout,
    children: [
      {
        path: "index",
        name: "businessIndex",
        component: () => import("../views/business/index.vue"),
      },
      {
        path: "business1",
        name: "business1",
        component: () => import("../views/business/business1.vue"),
      }
    ],
  },
  {
    path: "/assets",
    name: "assets",
    component: Layout,
    children: [
      {
        path: "index",
        name: "assetsIndex",
        component: () => import("../views/assets/index.vue"),
      },
    ],
  },
  {
    path: "/projectDetail",
    name: "projectDetail",
    component: Layout,
    children: [
      {
        path: "index",
        name: "projectDetailIndex",
        component: () => import("../views/projectDetail/index.vue"),
      },
    ],
  },
  {
    path: "/yingdi",
    name: "yingdi",
    component: Layout,
    children: [
      {
        path: "index",
        name: "yingdiIndex",
        component: () => import("../views/yingdi/index.vue"),
      },
    ],
  },
  {
    path: "/yibuyiping",
    name: "yibuyiping",
    component: Layout,
    children: [
      {
        path: "index",
        name: "yibuyipingIndex",
        component: () => import("../views/yibuyiping/index.vue"),
      },
      {
        path: "yibuyipingDetail",
        name: "yibuyipingDetail",
        component: () => import("../views/yibuyiping/detail.vue"),
      },
      {
        path: "zhiliang",
        name: "yibuyipingZhiliang",
        component: () => import("../views/yibuyiping/zhiliang.vue"),
      },
      {
        path: "anquan",
        name: "yibuyipingAnquan",
        component: () => import("../views/yibuyiping/anquan.vue"),
      },
      {
        path: "guonei",
        name: "yibuyipingGuonei",
        component: () => import("../views/yibuyiping/guonei.vue"),
      },
      {
        path: "guoji",
        name: "yibuyipingGuoji",
        component: () => import("../views/yibuyiping/guoji.vue"),
      },
      {
        path: "huanjing",
        name: "yibuyipingHuanjing",
        component: () => import("../views/yibuyiping/huanjing.vue"),
      },
    ],
  },
  {
    path: "/liwulv",
    name: "liwulv",
    component: Layout,
    children: [
      {
        path: "index",
        name: "liwulvIndex",
        component: () => import("../views/liwulv/index.vue"),
      },
    ],
  },
  {
    path: "/xianchangshipin",
    name: "xianchangshipin",
    component: Layout,
    children: [
      {
        path: "index",
        name: "xianchangshipinIndex",
        component: () => import("../views/xianchangshipin/index.vue"),
      },
    ],
  },

  // 仅用于自动登录检测的路由
  {
    path: "/noAuth",
    name: "noAuth",
    component: () => import("../views/AutoLogin.vue"), // 自动登录占位组件，实际逻辑在路由守卫中处理
  },
  // 免登录直接访问一部一屏详情页面的快捷路径（主要目标）
  {
    path: "/auto-detail",
    name: "autoDetail",
    redirect: to => {
      // 重定向到免登录路径，并附带跳转参数
      return { path: '/noAuth', query: { redirect: 'detail' } }
    }
  },
  // 免登录直接访问质量安全部页面的快捷路径
  {
    path: "/auto-anquan",
    name: "autoAnquan",
    redirect: to => {
      // 重定向到免登录路径，并附带跳转参数
      return { path: '/noAuth', query: { redirect: 'anquan' } }
    }
  },
  // 免登录直接访问质量管理页面的快捷路径
  {
    path: "/auto-zhiliang",
    name: "autoZhiliang",
    redirect: to => {
      // 重定向到免登录路径，并附带跳转参数
      return { path: '/noAuth', query: { redirect: 'zhiliang' } }
    }
  },
  // 免登录直接访问环境管理页面的快捷路径
  {
    path: "/auto-huanjing",
    name: "autoHuanjing",
    redirect: to => {
      // 重定向到免登录路径，并附带跳转参数
      return { path: '/noAuth', query: { redirect: 'huanjing' } }
    }
  },
  // 免登录直接访问国内业务页面的快捷路径
  {
    path: "/auto-guonei",
    name: "autoGuonei",
    redirect: to => {
      // 重定向到免登录路径，并附带跳转参数
      return { path: '/noAuth', query: { redirect: 'guonei' } }
    }
  },
  // 免登录直接访问国际业务页面的快捷路径
  {
    path: "/auto-guoji",
    name: "autoGuoji",
    redirect: to => {
      // 重定向到免登录路径，并附带跳转参数
      return { path: '/noAuth', query: { redirect: 'guoji' } }
    }
  },
  // 通配路由，将所有未匹配的路由重定向到登录页
  {
    path: "/:pathMatch(.*)*",
    redirect: to => {
      // 只有精确匹配/noAuth才保持原样，用于触发自动登录
      if (to.path === '/noAuth') {
        console.log('通配路由: 保持/noAuth');
        return '/noAuth'
      }
      // 其他所有未匹配路由重定向到登录页
      console.log('通配路由: 重定向到登录页:', to.path);
      return '/login'
    }
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
