<template>
    <div class="yibuyiping-detail-container-simple">
        <!-- 返回按钮 -->
        <img class="cursor-pointer absolute top-[30px] left-[60px]" src="@/assets/images/xiangmu/back.png" alt=""
            @click="goBack" style="z-index: 1000" />

        <div class="main-content">
            <h1 style="color: white; text-align: center; margin-top: 100px;">质量安全大屏 - 测试页面</h1>

            <!-- 简化的统计指标 -->
            <div class="stats-container">
                <div class="stat-item">
                    <h3>绿色施工警数</h3>
                    <div class="number">126<span>个</span></div>
                </div>
                <div class="stat-item">
                    <h3>AI预警数量</h3>
                    <div class="number">67<span>个</span></div>
                </div>
                <div class="stat-item">
                    <h3>视频监控设备数</h3>
                    <div class="number">78<span>个</span></div>
                </div>
                <div class="stat-item">
                    <h3>危大工程数</h3>
                    <div class="number">12<span>个</span></div>
                </div>
            </div>

            <!-- 测试导航按钮 -->
            <div class="nav-buttons">
                <button @click="goToSafety" class="nav-btn">安全页面</button>
                <button @click="goToQuality" class="nav-btn">质量页面</button>
                <button @click="goToEnvironment" class="nav-btn">环境页面</button>
            </div>

            <!-- 调试信息 -->
            <div class="debug-info">
                <h4>调试信息:</h4>
                <p>当前路由: {{ $route.path }}</p>
                <p>页面加载时间: {{ loadTime }}</p>
                <p>错误信息: {{ errorMessage || '无' }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const loadTime = ref('');
const errorMessage = ref('');

const goBack = () => {
    router.push('/yibuyiping/index');
};

// 跳转到安全页面
const goToSafety = () => {
    router.push('/yibuyiping/anquan');
};

// 跳转到质量页面
const goToQuality = () => {
    router.push('/yibuyiping/zhiliang');
};

// 跳转到环境页面
const goToEnvironment = () => {
    router.push('/yibuyiping/huanjing');
};

onMounted(() => {
    loadTime.value = new Date().toLocaleTimeString();
    console.log('简化版detail页面已加载');

    // 测试控制台日志
    console.log('当前路由:', route.path);
    console.log('路由参数:', route.params);
    console.log('路由查询:', route.query);
});
</script>

<style scoped lang="scss">
.yibuyiping-detail-container-simple {
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #0a1628 0%, #1a2744 100%);
    color: white;
    padding: 20px;
    position: relative;
}

.main-content {
    padding-top: 60px;
}

.stats-container {
    display: flex;
    justify-content: space-around;
    margin: 50px 0;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(0, 200, 255, 0.1);
    border: 1px solid rgba(0, 200, 255, 0.3);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    min-width: 200px;
    margin: 10px;

    h3 {
        color: #00c8ff;
        margin-bottom: 10px;
        font-size: 16px;
    }

    .number {
        font-size: 22px; // PC端字体大小调整
        font-weight: bold;
        color: white;

        span {
            font-size: 14px; // PC端字体大小调整
            color: rgba(255, 255, 255, 0.7);
            margin-left: 4px;
        }
    }
}

.nav-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 50px 0;
}

.nav-btn {
    background: rgba(0, 200, 255, 0.8);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;

    &:hover {
        background: rgba(0, 200, 255, 1);
        transform: translateY(-2px);
    }
}

.debug-info {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 20px;
    margin-top: 50px;

    h4 {
        color: #00c8ff;
        margin-bottom: 10px;
    }

    p {
        margin: 5px 0;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
    }
}
</style>