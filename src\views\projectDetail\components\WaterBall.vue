<template>
  <div ref="chartRef" :style="{ width: width, height: height }"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from "vue";
  import * as echarts from "echarts";
  import "echarts-liquidfill";

  const props = defineProps({
    value: {
      type: Number,
      default: 0.5,
    },
    width: {
      type: String,
      default: "110px",
    },
    height: {
      type: String,
      default: "110px",
    },
    waveColor: {
      type: String,
      default: "#42b883",
    },
    title: {
      type: String,
      default: "整改率",
    },
  });

  const chartRef = ref(null);
  let chart = null;

  const initChart = () => {
    if (!chartRef.value) return;

    chart = echarts.init(chartRef.value);
    const option = {
      title: [
        {
          text:
            "{val|" +
            props.value * 100 +
            "}{unit|%}\n{name|" +
            props.title +
            "}",
          top: "center",
          left: "45%",
          textAlign: "center",
          show: props.value,
          textStyle: {
            rich: {
              name: {
                width: 40,
                fontSize: 14,
                fontWeight: "300",
                padding: [10, 0],
                color: "#fff",
              },
              val: {
                width: 50,
                fontSize: 32,
                color: "#fff",
                fontFamily: "Oswald Medium",
              },
              unit: {
                color: "#fff",
              },
            },
          },
        },
      ],
      series: [
        {
          type: "liquidFill",
          data: [
            {
              value: props.value,
              phase: 0,
            },
            {
              value: props.value,
              phase: Math.PI,
            },
            {
              value: props.value - 0.3,
              phase: Math.PI / 2,
            },
          ],
          radius: "90%",
          color: [props.waveColor, props.waveColor, props.waveColor],
          backgroundStyle: {
            color: "transparent",
          },
          outline: {
            show: false,
            borderDistance: 5,
            itemStyle: {
              color: "none",
              borderColor: props.waveColor,
              borderWidth: 2,
              shadowBlur: 0,
              shadowColor: "rgba(0, 0, 0, 0)",
            },
          },
          label: {
            show: false,
          },
          itemStyle: {
            opacity: 0.85,
          },
          emphasis: {
            itemStyle: {
              opacity: 0.7,
            },
          },
          amplitude: 8,
          period: 4000,
          waveLength: "100%",
          phase: 0,
          direction: "right",
        },
      ],
    };

    chart.setOption(option);
  };

  onMounted(() => {
    initChart();
    window.addEventListener("resize", chart?.resize);
  });

  onUnmounted(() => {
    chart?.dispose();
    window.removeEventListener("resize", chart?.resize);
  });
</script>
