<template>
    <div class="project-detail-containers">
      <div class="content relative">
        <div
          class="absolute w-1/2 text-center text-[28px] font-family-youshebiaotihei tracking-[5px] leading-[60px] title z-10 top-0 left-2/4 transform -translate-x-1/2" style="z-index: 31; text-shadow: 0 2px 4px rgba(0,0,0,0.7);">
          {{ title }}
        </div>
        <div
          class="absolute text-center text-[18px] font-family-youshebiaotihei tracking-[5px] title z-10 top-[80px] left-2/4 transform -translate-x-1/2" style="text-shadow: 0 2px 26px rgba(0,0,0,0.7);">
          {{ projectData.项目名称 }}</div>
        <div class="bg"></div>
        <div class="leftBg"></div>
        <div class="bottomBg"></div>
        
        <!-- 左侧按钮 -->
        <div class="btns">
          <div v-for="(item, index) in filteredBtns" 
            :class="[
              btnsActive == index && 'active', 
              (isInternationalProject && index > 0 || isDesignProject && index > 0) && 'disabled'
            ]" 
            :key="index"
            @click="handleBtnClick(index)">
            <span> {{ item.name }}</span>
          </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-content">
          <!-- 左边内容展示区域 (4/10) -->
          <div class="left-content-container">
            <item1 :projectData="projectData" :isInternationalProject="isInternationalProject" :personData="personData" v-if="btnsActive == 0" />
            <item2 :projectData="projectData" v-if="btnsActive == 1" />
            <item3 :projectData="projectData" v-if="btnsActive == 2" />
          </div>
          
          <!-- 右边图片展示区域 (6/10) -->
          <div class="right-image-section">
              <!-- 右边主要图片展示区域 -->
    <div class="right-image-container">
      <div class="image-display-area">
        <img v-if="rightImageSrc && currentMediaType === 'image'" :src="rightImageSrc" alt="右侧展示图片" />
        <div v-else-if="!rightImageSrc && !rightVideoSrc && mediaItems.length === 0" class="no-image-placeholder">
          <span>暂无内容</span>
        </div>
      </div>
    </div>
            
            <!-- 最右边缩略图容器 -->
            <div v-if="!isUsingDefaultImage && (images.length > 0 || videos.length > 0)" class="thumbnails-container">
              <div v-for="(mediaItem, index) in mediaItems" :key="index" class="thumbnail"
                :class="{ 'active': currentMediaIndex === index }">
                <!-- 图片缩略图 -->
                <img v-if="mediaItem.type === 'image'" :src="mediaItem.thumbnail" 
                  :alt="t('projectDetail.actions.thumbnailAlt')"
                  @click="() => handleThumbnailClick(index, mediaItem)" />
                
                <!-- 视频缩略图 -->
                <div v-else-if="mediaItem.type === 'video'" class="video-thumbnail-container"
                  @click="() => handleThumbnailClick(index, mediaItem)">
                  <video :src="mediaItem.thumbnail" 
                    class="video-thumbnail" 
                    muted 
                    preload="metadata"
                    @loadedmetadata="($event) => {
                      $event.target.currentTime = 2; // 设置为2秒处的帧作为缩略图
                    }">
                  </video>
                  <div class="video-play-icon">
                    <svg viewBox="0 0 24 24" width="24" height="24">
                      <path fill="currentColor" d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 删除悬浮覆盖层和按钮，因为内容现在直接显示在左边 -->
        
        <!-- 返回首页按钮 -->
        <div class="backHome-button" @click="backHome">
          <img src="@/assets/images/header/back.png" alt="" />
          <span>返回首页</span>
        </div>
        <!-- 返回上一级按钮 -->
        <div class="back-button" @click="back">
          <span>返回上一级</span>
        </div>
      </div>
    </div>
  </template>
  <script setup>
  import { onMounted, ref, watch, computed } from "vue";
  import { useRouter, useRoute } from "vue-router";
  import { useI18n } from 'vue-i18n';
  import item1 from "./components/item1.vue";
  import item2 from "./components/item2.vue";
  import item3 from "./components/item3.vue";
  import request from "@/utils/request";
  import defaultMapImg from '@/assets/images/project/mapImg.png';
  const router = useRouter();
  const route = useRoute();
  const { t } = useI18n();
  
  const title = ref("");
  const projectName = localStorage.getItem("clickProject");
  const projectData = ref({});
  const personData = ref({}); // 新增人员数据响应式变量
  
  const isChina = ref(localStorage.getItem("isChina") ? true : false);
  
  // 判断是否为国际项目
  const isInternationalProject = ref(false);
  
  // 新增：判断是否为设计类项目
  const isDesignProject = computed(() => {
    return projectData.value && projectData.value.项目类别 === '设计';
  });
  
  const btns = ref([
    {
      id: 1,
      name: t('projectDetail.tabs.overview'),
    },
    {
      id: 2,
      name: t('projectDetail.tabs.qualitySafety'),
    },
    {
      id: 3,
      name: t('projectDetail.tabs.equipmentFacilities'),
    },
  ]);
  
  // 计算过滤后的按钮列表
  const filteredBtns = computed(() => {
    return btns.value.map(btn => ({
      ...btn,
      name: btn.id === 1 ? t('projectDetail.tabs.overview') :
        btn.id === 2 ? t('projectDetail.tabs.qualitySafety') :
          btn.id === 3 ? t('projectDetail.tabs.equipmentFacilities') :
            btn.name
    }));
  });
  
  const btnsActive = ref(0);
  const btnsChange = (val) => {
    btnsActive.value = val;
    localStorage.setItem("btnsActive", val);
  };

  // 新增处理按钮点击逻辑
  const handleBtnClick = (index) => {
    // 对于国际项目或设计类项目，只允许点击第一个标签（项目概况）
    if ((isInternationalProject.value || isDesignProject.value) && index > 0) {
      return; // 不做任何操作，禁止切换
    }
    btnsChange(index);
  };
  
  const images = ref([]);
  const currentImageIndex = ref(0);
  // 添加标识是否使用默认图片
  const isUsingDefaultImage = ref(true);

  // 添加视频相关变量
  const videos = ref([]);
  const mediaItems = ref([]); // 合并的媒体项目（照片+视频）
  const currentMediaIndex = ref(0);
  const currentMediaType = ref('image'); // 'image' 或 'video'
  
  // 右侧图片源
  const rightImageSrc = computed(() => {
    if (currentMediaType.value === 'image' && images.value.length > 0) {
      return images.value[currentImageIndex.value];
    }
    return null;
  });

  // 右侧视频源  
  const rightVideoSrc = computed(() => {
    if (currentMediaType.value === 'video' && videos.value.length > 0) {
      return videos.value[currentMediaIndex.value - images.value.length];
    }
    return null;
  });
  
    // 已移除未使用的 generateImageUrls 函数
  
  // 照片处理函数
  async function getProjectPhotoUrls(photoIds) {
    if (!photoIds) {
      return [defaultMapImg];
    }
  
    try {
      // 先获取token
      const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");
  
      if (tokenRes.code !== 0 || !tokenRes.data) {
        console.error("获取token失败:", tokenRes);
        return [defaultMapImg];
      }
  
      const token = tokenRes.data;
      const userId = "941981453197164545"; // 固定的userId
  
      // 处理照片ID列表
      const photoIdArray = photoIds.split(',').filter(id => id.trim());
  
      if (photoIdArray.length === 0) {
        return [defaultMapImg];
      }
  
      const photoUrls = [];
  
      // 限制处理最多5张图片
      const maxPhotos = Math.min(photoIdArray.length, 5);
  
      // 验证每个图片URL是否可访问
      for (let i = 0; i < maxPhotos; i++) {
        const photoId = photoIdArray[i].trim();
        if (photoId) {
          try {
            // 构建照片URL
            const photoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${photoId}?access_token=${token}&userid=${userId}`;
  
            // 验证图片是否可访问
            const isValid = await validateImageUrl(photoUrl);
            if (isValid) {
              photoUrls.push(photoUrl);
            }
          } catch (err) {
            console.warn(`图片${photoId}处理失败:`, err);
            continue;
          }
        }
      }
  
      return photoUrls.length > 0 ? photoUrls : [defaultMapImg];
    } catch (err) {
      console.error("处理照片失败:", err);
      return [defaultMapImg];
    }
  }
  
  // 验证图片URL是否可访问
  function validateImageUrl(url) {
    return new Promise((resolve) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        resolve(false);
      }, 5000); // 5秒超时
  
      img.onload = () => {
        clearTimeout(timeout);
        resolve(true);
      };
  
      img.onerror = () => {
        clearTimeout(timeout);
        resolve(false);
      };
  
      img.src = url;
    });
  }

  // 视频处理函数
  async function getProjectVideoUrls(videoIds) {
    console.log('🎥 开始处理视频，videoIds:', videoIds);
    
    if (!videoIds) {
      console.log('❌ videoIds为空，返回空数组');
      return [];
    }

    try {
      // 先获取token
      console.log('🔑 开始获取token...');
      const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");
      console.log('🔑 token响应:', tokenRes);

      if (tokenRes.code !== 0 || !tokenRes.data) {
        console.error("❌ 获取token失败:", tokenRes);
        return [];
      }

      const token = tokenRes.data;
      const userId = "941981453197164545"; // 固定的userId
      console.log('✅ token获取成功:', token);

      // 处理视频ID列表
      const videoIdArray = videoIds.split(',').filter(id => id.trim());
      console.log('📋 处理后的视频ID数组:', videoIdArray);

      if (videoIdArray.length === 0) {
        console.log('❌ 视频ID数组为空，返回空数组');
        return [];
      }

          // 限制处理最多5个视频
    const maxVideos = Math.min(videoIdArray.length, 5);
    console.log(`🎬 准备处理 ${maxVideos} 个视频`);

    // 直接构建视频URL（不验证，跟item3.vue保持一致）
    const videoUrls = videoIdArray.slice(0, maxVideos).map(videoId => {
      const trimmedId = videoId.trim();
      const videoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${trimmedId}?access_token=${token}&userid=${userId}`;
      console.log(`🔗 构建的视频URL: ${videoUrl}`);
      return videoUrl;
    });

      console.log(`🎉 最终返回的视频URLs:`, videoUrls);
      return videoUrls;
    } catch (err) {
      console.error("❌ 处理视频失败:", err);
      return [];
    }
  }



  // 合并媒体项目的函数
  function mergeMediaItems() {
    const items = [];
    
    // 1. 处理效果图（如果存在）
    if (projectData.value && projectData.value.项目效果图) {
      // 处理效果图ID列表（可能用逗号分割）
      const effectImageIds = projectData.value.项目效果图.split(',').filter(id => id.trim());
      console.log('发现效果图ID列表:', effectImageIds);
      
      // 查找所有效果图URL并添加为优先项
      effectImageIds.forEach((effectImageId, index) => {
        const trimmedId = effectImageId.trim();
        const effectImageUrl = images.value.find(imgUrl => imgUrl.includes(trimmedId));
        
        if (effectImageUrl) {
          // 添加效果图作为优先项
          items.push({
            type: 'image',
            url: effectImageUrl,
            index: items.length,
            thumbnail: effectImageUrl,
            isEffectImage: true // 标记为效果图
          });
          console.log(`添加效果图${index + 1}:`, effectImageUrl);
        } else {
          console.log(`未找到对应效果图URL，效果图ID为:`, trimmedId);
        }
      });
    } else {
      console.log('项目数据中没有效果图ID');
    }
    
    // 2. 添加其他普通图片（排除已添加的效果图）
    const effectImageUrls = items.filter(item => item.isEffectImage).map(item => item.url);
    
    images.value.forEach((image) => {
      // 如果当前图片是效果图，则跳过（避免重复添加）
      if (effectImageUrls.includes(image)) {
        return;
      }
      
      items.push({
        type: 'image',
        url: image,
        index: items.length,
        thumbnail: image
      });
    });
    
    // 3. 添加视频项目（放在最后）
    if (videos.value.length > 0) {
      console.log(`添加${videos.value.length}个视频项目`);
      videos.value.forEach((video) => {
        items.push({
          type: 'video',
          url: video,
          index: items.length,
          thumbnail: video // 视频缩略图使用视频本身
        });
      });
    } else {
      console.log('没有视频项目可添加');
    }
    
    mediaItems.value = items;
    console.log(`合并后的媒体项目共${items.length}个:`, mediaItems.value);
    
    // 如果有媒体项目，设置第一个为活动状态
    if (items.length > 0) {
      currentMediaIndex.value = 0;
      const firstItem = items[0];
      if (firstItem.type === 'image') {
        currentImageIndex.value = 0;
        currentMediaType.value = 'image';
      } else if (firstItem.type === 'video') {
        currentMediaType.value = 'video';
      }
      
      // 初始化显示第一个媒体项目
      setTimeout(() => {
        handleThumbnailClick(0, firstItem);
      }, 100);
    }
  }
  
  // 获取项目数据
  async function fetchProjectData() {
    try {
      let projectDatas = {};
      let queryData = {}
      try {
        // 从sessionStorage获取项目数据而不是URL参数
        queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
      } catch (e) {
        console.error("解析项目数据失败", e);
      }
      const projectId = queryData.code || "";
      const isGwParam = queryData.isGw || "false";

      // 转换isGw参数，确保是字符串类型
      const isGw = String(isGwParam).toLowerCase();

      // 设置国际项目标识
      isInternationalProject.value = isGw === "true";

      let res;
      if (isGw === "true") {
        // 国外项目接口
        res = await request.get("/globalManage/zjmanage/largescreen/getXmxxV2", {
          params: { id: projectId }
        });
      } else {
        // 国内项目接口
        res = await request.get("/globalManage/zjmanage/largescreen/getXmjbxx", {
          params: { id: projectId }
        });
      }

      if (res.code === 0 && res.data && res.data.length > 0) {
        const data = res.data[0];

        if (isGw === "true") {
          // 设置标题为国家名
          title.value = data.国家 || "";
          projectData.value = data;
          
          // 国际项目：使用原有照片逻辑
          await processProjectPhotos(data);
          
          // 确保国际项目的效果图和视频都能被处理
          console.log('正在处理国际项目数据');
          console.log('项目效果图:', data.项目效果图);
          console.log('项目视频:', data.项目视频);
        } else {
          // 国内项目数据映射
          projectDatas = {
            项目名称: data.项目名称 || "",
            合同金额: data.合同金额 || "0",
            产值: data.产值 || "0",
            工程回款总额: data.工程回款总额 || "0",
            项目进度: data.施工进度 ? (Number(data.施工进度) * 100) + '%' : '0%',
            开工日期: data.开工日期 || "",
            总工期: data.总工期 || "",
            照片: data.项目照片 || "",
            视频: data.项目视频 || "",
            项目总人数: data.项目总人数 || "0",
            正式员工: data.正式员工 || "0",
            劳务派遣: data.劳务派遣 || "0",
            其他形式用工: data.其他形式用工 || "0",
            合作单位: data.合作单位人员 || "0",
            类型: "项目",
            省: data.省 || "",
            市: data.市 || "",
            区: data.区 || "",
            项目经度: data.项目经度 || "",
            项目纬度: data.项目纬度 || "",
            code: data.code || "" // 确保code字段传递给项目数据
          };

          // 设置标题为省份名
          title.value = data.省 || "";
          projectData.value = data;

          // 国内项目：调用渲染接口获取渲染文件
          if (data.code) {
            try {
              const renderingRes = await request.post("/globalManage/zjmanage/largescreen/getRenderingByCode", {
                projectCode: data.code
              });
              
              if (renderingRes.code === 0 && renderingRes.data && renderingRes.data.length > 0) {
                console.log("获取渲染文件成功:", renderingRes.data);
                // 将渲染文件信息添加到项目数据中
                projectData.value.renderingFiles = renderingRes.data;
                
                // 提取渲染文件的下载链接作为展示图片
                const renderingImages = renderingRes.data
                  .filter(file => file.downloadUrl) // 过滤掉没有下载链接的文件
                  .map(file => file.downloadUrl);
                
                if (renderingImages.length > 0) {
                  // 使用渲染文件作为展示图片
                  images.value = renderingImages;
                  isUsingDefaultImage.value = false;
                  console.log("使用渲染文件作为展示图片:", renderingImages);
                } else {
                  // 如果渲染文件没有有效的下载链接，则使用原有照片逻辑
                  await processProjectPhotos(data);
                }
              } else {
                console.warn("获取渲染文件失败或无数据:", renderingRes);
                projectData.value.renderingFiles = [];
                // 没有渲染文件时使用原有照片逻辑
                await processProjectPhotos(data);
              }
            } catch (renderingErr) {
              console.error("调用渲染接口失败:", renderingErr);
              projectData.value.renderingFiles = [];
              // 接口调用失败时使用原有照片逻辑
              await processProjectPhotos(data);
            }
          } else {
            console.warn("项目数据中没有code字段，跳过渲染接口调用");
            projectData.value.renderingFiles = [];
            // 没有code字段时使用原有照片逻辑
            await processProjectPhotos(data);
          }
        }

        // 保存视频信息
        if (data.项目视频) {
          // 可以在这里处理视频信息，如果需要的话
          // videoUrl.value = data.项目视频;
        }
      } else {
        // 接口返回错误，设置默认图片
        images.value = [defaultMapImg];
        isUsingDefaultImage.value = true;
      }
    } catch (err) {
      console.error("获取项目数据失败:", err);
      // 接口异常，设置默认图片
      images.value = [defaultMapImg];
      isUsingDefaultImage.value = true;
    }
  }

  // 处理项目照片的独立函数
  async function processProjectPhotos(data) {
    console.log('processProjectPhotos收到的原始data:', data);
    console.log('data.项目照片:', data.项目照片);
    console.log('data.项目效果图:', data.项目效果图);
    console.log('data.项目视频:', data.项目视频);
    
    // 用于跟踪是否找到了任何媒体内容
    let hasAnyMedia = false;
    
    // 处理照片
    if (data.项目照片) {
      try {
        // 使用新的照片处理函数
        const photoUrls = await getProjectPhotoUrls(data.项目照片);

        // 检查是否获取到有效的图片URL
        if (photoUrls.length > 0 && photoUrls[0] !== defaultMapImg) {
          images.value = photoUrls;
          hasAnyMedia = true; // 找到了有效的照片
        } else {
          // 如果处理失败，设为空数组
          images.value = [];
        }
      } catch (err) {
        console.error("处理项目图片失败:", err);
        images.value = [];
      }
    } else {
      // 没有项目照片字段
      images.value = [];
    }

    // 处理项目效果图（如果有）
    let hasEffectImage = false;
    if (data.项目效果图) {
      try {
        // 获取token
        const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");
        if (tokenRes.code === 0 && tokenRes.data) {
          const token = tokenRes.data;
          const userId = "941981453197164545"; // 固定的userId
          
          // 处理效果图ID列表（可能用逗号分割）
          const effectImageIds = data.项目效果图.split(',').filter(id => id.trim());
          console.log("效果图ID列表:", effectImageIds);
          
          // 逐个处理效果图
          for (let i = 0; i < effectImageIds.length; i++) {
            const effectImageId = effectImageIds[i].trim();
            if (effectImageId) {
              const effectImageUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${effectImageId}?access_token=${token}&userid=${userId}`;
              
              // 验证效果图URL是否有效
              const isValid = await validateImageUrl(effectImageUrl);
              if (isValid) {
                console.log(`效果图${i + 1}URL有效:`, effectImageUrl);
                
                // 检查images数组中是否已包含此效果图
                const exists = images.value.some(img => img === effectImageUrl);
                if (!exists) {
                  // 如果不存在，则添加到images数组，后续会在mergeMediaItems中特殊处理
                  images.value.push(effectImageUrl);
                  hasEffectImage = true;
                  hasAnyMedia = true; // 找到了有效的效果图
                  console.log(`效果图${i + 1}已添加到图片列表`);
                }
              } else {
                console.warn(`效果图${i + 1}URL无效:`, effectImageUrl);
              }
            }
          }
        }
      } catch (err) {
        console.error("处理项目效果图失败:", err);
      }
    }

    // 处理项目视频
    let hasVideos = false;
    if (data.项目视频) {
      try {
        const videoUrls = await getProjectVideoUrls(data.项目视频);
        if (videoUrls.length > 0) {
          videos.value = videoUrls;
          hasVideos = true;
          hasAnyMedia = true; // 找到了有效的视频
          console.log("获取项目视频成功:", videoUrls);
        } else {
          videos.value = [];
        }
      } catch (err) {
        console.error("处理项目视频失败:", err);
        videos.value = [];
      }
    } else {
      videos.value = [];
      console.log('未发现项目视频');
    }

    // 只有在三种媒体都不存在的情况下，才使用默认图片
    if (!hasAnyMedia) {
      console.log('没有找到任何媒体内容，使用默认图片');
      images.value = [defaultMapImg];
      isUsingDefaultImage.value = true;
    } else {
      console.log(`找到媒体内容: 照片=${images.value.length > 0}, 效果图=${hasEffectImage}, 视频=${hasVideos}`);
      isUsingDefaultImage.value = false;
    }

    console.log('处理后的images数组:', images.value);

    // 合并媒体项目
    mergeMediaItems();
  }

  // 获取国内项目人员数据
  async function getPersonData() {
    try {
      const queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
      const projectCode = queryData.code || "";

      if (!projectCode) {
        console.warn("项目code为空，无法获取人员数据");
        return {};
      }

      // 使用POST方法调用接口
      const res = await request.post("/globalManage/deviceMonitor/getNumberPerson", {
        projectCode: projectCode
      });

      console.log("获取人员数据响应:", res);

      if (res.code === 0 && res.data) {
        return res.data;
      } else {
        console.warn("获取人员数据失败或无数据:", res);
        return {};
      }
    } catch (err) {
      console.error("获取人员数据失败:", err);
      return {};
    }
  }
  
  onMounted(() => {
    // 使用新的统一接口获取数据
    fetchProjectData().then(() => {
      // 在项目数据获取完成后，判断是否为国内项目，只有国内项目才获取人员数据
      const queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
      const isGw = queryData.isGw === "true";
      
      if (!isGw) {
        // 国内项目：获取人员数据
        getPersonData().then(data => {
          personData.value = data;
          console.log("国内项目人员数据:", data);
        });
      }
    });
  });

  watch([images, videos], () => {
    mergeMediaItems();
  });

/**
 * 返回上一级 - 返回到国家/地区详情页面
 * 特殊处理：江苏省项目返回到市一级，其他省份项目返回到省一级
 */
 const back = () => {
  console.log('项目详情页面返回上一级');
  
  // 构建要传递的数据
  const transferData = {
    level: '',
    title: ''
  };

  // 判断是国内还是国外项目
  const clickProject = sessionStorage.getItem("clickProject");
  if (clickProject) {
    try {
      const queryData = JSON.parse(clickProject || "{}");
      const isGw = queryData.isGw === "true";

      if (isGw) {
        // 国际项目：传递level和国家
        transferData.level = 'country';
        transferData.title = projectData.value.国家 || title.value;
        console.log(`国际项目返回到国家级: ${transferData.title}`);
      } else {
        // 国内项目：根据省份判断返回层级
        const province = projectData.value.省 || '';
        const city = projectData.value.市 || '';
        const district = projectData.value.区 || '';
        
        console.log(`项目所在地: 省=${province}, 市=${city}, 区=${district}`);
        
        // 标准化省份名称
        const normalizedProvince = normalizeProvinceName(province);
        console.log(`标准化后的省份名称: ${normalizedProvince}`);
        
        // 江苏省特殊处理：返回到市一级
        if (normalizedProvince === '江苏省') {
          if (city) {
            transferData.level = 'city';
            transferData.title = city;
            console.log(`江苏省项目返回到市级: ${city}`);
          } else {
            // 如果没有市级数据，退回到省级
            transferData.level = 'province';
            transferData.title = normalizedProvince;
            console.log(`江苏省项目缺少市级数据，返回到省级: ${normalizedProvince}`);
          }
        } else {
          // 其他省份：直接返回到省一级
          if (normalizedProvince) {
            transferData.level = 'province';
            transferData.title = normalizedProvince;
            console.log(`非江苏省项目返回到省级: ${normalizedProvince}`);
          } else {
            // 如果连省份都没有，尝试使用市级数据
            if (city) {
              transferData.level = 'city';
              transferData.title = city;
              console.log(`缺少省份数据，使用市级数据: ${city}`);
            } else if (district) {
              transferData.level = 'district';
              transferData.title = district;
              console.log(`缺少省市数据，使用区级数据: ${district}`);
            } else {
              console.warn('项目地理位置数据不完整，默认返回中国');
              transferData.level = 'country';
              transferData.title = '中国';
            }
          }
        }
      }
    } catch (e) {
      console.error("解析项目数据失败", e);
      // 默认返回中国
      transferData.level = 'country';
      transferData.title = '中国';
    }
  } else {
    console.warn('未找到项目数据，默认返回中国');
    transferData.level = 'country';
    transferData.title = '中国';
  }

  // 使用sessionStorage存储数据而不是通过URL参数
  sessionStorage.setItem("countryLevel", transferData.level);
  sessionStorage.setItem("countryTitle", transferData.title);
  // 新增：保存点击的国家/区域，便于业务布局页面恢复状态
  sessionStorage.setItem("clickCountry", transferData.title);
  // 新增：设置国内/国际项目标识（国内项目：1，国际项目：0）
  const isInternational = transferData.level === "country" && transferData.title !== "中国";
  localStorage.setItem("isChina", isInternational ? "0" : "1");
  
  // 设置返回上一级标记
  sessionStorage.setItem("returnType", "backToCountry");
  console.log('🔵 项目详情页面：设置returnType为backToCountry');
  console.log('🔵 当前sessionStorage状态:', {
    returnType: sessionStorage.getItem("returnType"),
    countryLevel: transferData.level,
    countryTitle: transferData.title,
    clickCountry: transferData.title,
    isChina: localStorage.getItem("isChina")
  });
  
  // 清理项目相关状态
  localStorage.removeItem("clickProject");
  sessionStorage.removeItem("clickProject");
  
  // 向iframe发送"cancel"消息（返回上一级的特定消息）
  setTimeout(() => {
    const iframe = document.getElementById("iframe");
    if (iframe && iframe.contentWindow) {
      console.log('向iframe发送返回上一级消息: cancel');
      iframe.contentWindow.postMessage(
        { eve: "cancel" },
        "*"
      );
    }
  }, 100);
  
  // 修改：跳转回业务布局首页
  router.replace({ path: "/business-display" });
};

/**
 * 标准化省份名称的辅助函数
 * @param {string} provinceName - 原始省份名称
 * @returns {string} - 标准化后的省份名称
 */
const normalizeProvinceName = (provinceName) => {
  if (!provinceName) return '';
  
  // 去除前后空格
  const trimmedName = provinceName.trim();
  
  // 标准化江苏省的各种表达方式
  if (trimmedName.includes('江苏')) {
    return '江苏省';
  }
  
  // 处理其他省份，确保省份名称格式正确
  const provinceMap = {
    '浙江': '浙江省',
    '广东': '广东省', 
    '山东': '山东省',
    '河南': '河南省',
    '四川': '四川省',
    '湖北': '湖北省',
    '湖南': '湖南省',
    '河北': '河北省',
    '安徽': '安徽省',
    '福建': '福建省',
    '江西': '江西省',
    '云南': '云南省',
    '贵州': '贵州省',
    '山西': '山西省',
    '陕西': '陕西省',
    '甘肃': '甘肃省',
    '青海': '青海省',
    '海南': '海南省',
    '辽宁': '辽宁省',
    '吉林': '吉林省',
    '黑龙江': '黑龙江省',
    '台湾': '台湾省'
  };
  
  // 检查是否在省份映射表中
  for (const [shortName, fullName] of Object.entries(provinceMap)) {
    if (trimmedName.includes(shortName)) {
      return fullName;
    }
  }
  
  // 处理直辖市和特别行政区
  const specialRegions = ['北京市', '上海市', '天津市', '重庆市', '香港特别行政区', '澳门特别行政区'];
  for (const region of specialRegions) {
    if (trimmedName.includes(region.replace('市', '').replace('特别行政区', ''))) {
      return region;
    }
  }
  
  // 处理自治区
  const autonomousRegions = {
    '内蒙古': '内蒙古自治区',
    '广西': '广西壮族自治区', 
    '西藏': '西藏自治区',
    '宁夏': '宁夏回族自治区',
    '新疆': '新疆维吾尔自治区'
  };
  
  for (const [shortName, fullName] of Object.entries(autonomousRegions)) {
    if (trimmedName.includes(shortName)) {
      return fullName;
    }
  }
  
  // 如果都不匹配，返回原始名称
  return trimmedName;
};

  /**
   * 返回首页 - 完全重置到业务展示页面的初始状态
   * 这是用户希望的"首页"状态：地球模式 + 左右数据展示
   */
  const backHome = () => {
    console.log('项目详情页面返回首页 - 完全重置状态');
    
    // 清除所有相关状态数据，确保返回到最初的页面状态
    localStorage.removeItem("isChina");
    localStorage.removeItem("clickProject");
    localStorage.removeItem("btnsActive");
    sessionStorage.removeItem("countryLevel");
    sessionStorage.removeItem("countryTitle");
    sessionStorage.removeItem("clickCountry");
    sessionStorage.removeItem("lastProjectState");
    sessionStorage.removeItem("clickProject");
    sessionStorage.removeItem("shouldResetToInitial");
    
    // 设置返回首页标记，确保业务展示页面恢复到初始状态
    sessionStorage.setItem("shouldResetToInitial", "true");
    sessionStorage.setItem("returnType", "backHome");
    
    // 跳转到业务布局首页
    router.replace({ path: "/business-display" });
  };

  /**
   * 处理缩略图点击切换
   * @param {number} index - 被点击的媒体项目索引
   * @param {Object} mediaItem - 被点击的媒体项目对象
   */
  const handleThumbnailClick = (index, mediaItem) => {
    // 先清除右侧容器内的所有内容
    const rightImageContainer = document.querySelector('.right-image-container .image-display-area');
    if (rightImageContainer) {
      rightImageContainer.innerHTML = '';
    }
    
    if (mediaItem.type === 'image') {
      // 图片处理
      currentImageIndex.value = mediaItem.index;
      currentMediaIndex.value = index;
      currentMediaType.value = 'image';
      
      // 创建并添加图片元素
      if (rightImageContainer) {
        const imgElement = document.createElement('img');
        imgElement.src = mediaItem.url;
        imgElement.alt = '项目图片';
        imgElement.style.width = '100%';
        imgElement.style.height = '100%';
        imgElement.style.objectFit = 'contain';
        rightImageContainer.appendChild(imgElement);
      }
    } else if (mediaItem.type === 'video') {
      // 视频处理
      currentMediaIndex.value = index;
      currentMediaType.value = 'video';
      
      // 创建视频元素
      if (rightImageContainer) {
        const videoElement = document.createElement('video');
        videoElement.src = mediaItem.url;
        videoElement.controls = true;
        videoElement.autoplay = true;
        videoElement.loop = true;
        videoElement.muted = false;
        videoElement.style.width = '100%';
        videoElement.style.height = '100%';
        videoElement.style.objectFit = 'contain';
        
        rightImageContainer.appendChild(videoElement);
      }
    }
  };
  </script>
  <style lang="scss" scoped>
.project-detail-containers {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #0a0f1a;
  z-index: 2;
}
  .content {
    width: 100%;
    height: 100%;
    pointer-events: all;
    position: relative;
    z-index: 1000;
  
    .bg {
      width: 100%;
      height: 150px;
      position: absolute;
      top: -28px;
      left: 0;
      background: url("@/assets/images/header/headerBgFont.png") no-repeat;
      background-size: 100% 100%;
      pointer-events: none;
      z-index: 30;
    }
  
    .topBg {
      width: 100%;
      height: 240px;
      position: absolute;
      top: 0;
      right: 0;
      background: url("@/assets/images/yingdi/top.png") no-repeat;
      background-size: 100% 100%;
      pointer-events: none;
      z-index: 2;
    }
  
    .bottomBg {
      width: 100%;
      height: 160px;
      position: absolute;
      left: 0;
      bottom: 0;
      // background: url("@/assets/images/yingdi/bottom.png") no-repeat;
      background-size: 100% 100%;
      pointer-events: none;
      z-index: 2;
    }
  
    .btns {
      width: 36px;
      height: 500px;
      position: absolute;
      top: 115px;
      left: 5px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      z-index: 6;
  
      >div {
        width: 36px;
        height: 160px;
        background: url("@/assets/images/project/btn.png") no-repeat;
        background-size: 100% 100%;
        color: #97acb4;
        box-sizing: border-box;
        padding: 0 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        cursor: pointer;
  
        &.active {
          background: url("@/assets/images/project/btnActive.png") no-repeat;
          background-size: 100% 100%;
          color: #fff;
        }
        
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          filter: grayscale(0.8);
          pointer-events: none;
        }
      }
    }
  
    // 主要内容区域
    .main-content {
      position: absolute;
      top: 70px;
      left: 50px;
      right: 20px; // 右边留出基本间距
      bottom: 50px;
      display: flex;
      gap: 20px;
      z-index: 1;
    }

    // 左边内容展示区域 (3/10)
    .left-content-container {
      flex: 3.1; // 占据3/10的宽度
      min-width: 280px;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 20px;
      overflow-y: auto;
      padding-right: 10px; /* Add some padding to separate from right section */

      /* Hide scrollbar for IE, Edge and Firefox */
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
      /* Hide scrollbar for Chrome, Safari and Opera */
      &::-webkit-scrollbar {
        display: none;
      }
    }

    // 右边图片展示区域 (7/10)
    .right-image-section {
      flex: 7; // 占据7/10的宽度
      display: flex;
      height: 100%;
      position: relative;
      gap: 20px; // 主图片和缩略图之间的间距
    }

    // 右边主要图片展示区域
    .right-image-container {
      flex: 1; // 占据右边区域的大部分
      height: 100%;
      
      .image-display-area {
        width: 100%;
        height: 100%;
        // border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        overflow: hidden;
        background: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          transition: all 0.3s ease;
        }
        
        .no-image-placeholder {
          color: rgba(255, 255, 255, 0.5);
          font-size: 18px;
          text-align: center;
        }
      }
    }

    // 缩略图容器样式 - 在右边区域内
    .thumbnails-container {
      width: 320px;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 12px;
      z-index: 8;
      pointer-events: auto;
      overflow-y: auto;
      flex-shrink: 0; // 防止缩略图区域被压缩
      padding-right: 8px; // 为了更好的视觉效果
      padding-top: 180px;
      
      // 隐藏滚动条但保留滚动功能
      scrollbar-width: none;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }
      
      // 添加滚动提示效果
      &:before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 2px;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }
      
      &:hover:before {
        opacity: 1;
      }
    }
  }
  .back-button {
      position: absolute;
      top: 25px;
      left: 170px;
      display: flex;
      align-items: center;
      gap: 8px;
      pointer-events: auto;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s ease;
      font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
      font-weight: normal;
      font-size: 16px;
      text-shadow: 0px 0px 4px #0085FF;
      text-align: right;
      font-style: normal;
      text-transform: none;
  }
  .backHome-button {
      position: absolute;
      top: 25px;
      left: 60px;
      display: flex;
      align-items: center;
      gap: 8px;
      pointer-events: auto;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s ease;
      font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
      font-weight: normal;
      font-size: 16px;
      text-shadow: 0px 0px 4px #0085FF;
      text-align: right;
      font-style: normal;
      text-transform: none;
      img{
        width: 18px;
        height: 18px;
      }
  }
  
  // 缩略图样式
  .thumbnail {
    width: 100%;
    height: 160px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    // overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    position: relative;
    
    &:hover {
      border-color: rgba(0, 89, 255, 0.6);
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 89, 255, 0.3);
    }
    
    &.active {
      border-color: #0080ff;
      box-shadow: 0 0 15px rgba(0, 47, 255, 0.6);
      transform: scale(1.05);
    }
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.3s ease;
      
      &:hover {
        opacity: 0.9;
      }
    }
    
    // 视频缩略图容器
    .video-thumbnail-container {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      
      .video-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: opacity 0.3s ease;
        
        &:hover {
          opacity: 0.9;
        }
      }
      
      .video-play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 48px;
        height: 48px;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(0, 89, 255, 0.8);
          transform: translate(-50%, -50%) scale(1.1);
        }
        
        svg {
          margin-left: 2px; // 稍微向右偏移，让播放图标更居中
        }
      }
    }
  }
  </style>
  