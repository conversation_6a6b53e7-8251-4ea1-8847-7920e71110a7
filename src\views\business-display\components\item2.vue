<template>
  <div class="left-content">
    <!-- 上半部分：安全隐患 -->
    <div class="left-item one">
      <!-- 第一行：安全隐患标题 -->
      <div class="project-title">
        {{ title }}
      </div>

      <!-- 第二行：标题栏 -->
      <div class="info-section">
        <div class="info-section-bg">{{ t('projectDetail.qualitySafety.safetyHazards') }}</div>
      </div>

      <!-- 第三行：排查记录和隐患记录 -->
      <div class="status-section">
        <div class="status-item">
          <svg-icon name="troubleshooting" color="#fff" style="width: 28px;height: 28px;margin-top: 4px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.inspectionRecords') }}: <span
                class="recordNum">{{
                  safetyData.排查记录 || 0 }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.types') }}</span>
            </div>
          </div>
        </div>
        <div class="status-item">
          <svg-icon name="hiddenDanger" color="#fff" style="width: 28px;height: 28px;margin-top: 4px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.hazardRecords') }}: <span
                class="recordNumRed">{{ safetyData.隐患记录 || 0 }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.types') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 第四行：已销项/待复查/待整改/超期 -->
      <div class="issue-section">
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.resolved') }}:</div>
            <div class="issue-value"> <span class="blueFont">{{ safetyData.已销项 || 0 }}</span> {{
              t('projectDetail.units.projects') }}</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingReview') }}:</div>
            <div class="issue-value"> <span class="yellowFont">{{ safetyData.待复查 || 0 }}</span> {{
              t('projectDetail.units.projects') }}</div>
          </div>
        </div>
        <div class="divider-line"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingRectification') }}</div>
            <div class="issue-value"> <span class="yellowFont">{{ safetyData.待整改 || 0 }}</span>{{
              t('projectDetail.units.projects') }}</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.overdue') }}</div>
            <div class="issue-value"> <span class="redFont">{{ safetyData.超期 || 0 }}</span>{{
              t('projectDetail.units.projects') }}</div>
          </div>
        </div>
      </div>

      <!-- 第五行：隐患数量图表 -->
      <div class="chart-section">
        <div class="chart">
          <BarChart :chartData="safetyChartData" color="255,213,0" topColor="223,192,66"></BarChart>
        </div>
      </div>

      <!-- 第二行：标题栏 -->
      <div class="info-section">
        <div class="info-section-bg">{{ t('projectDetail.qualitySafety.qualityIssues') }}</div>
      </div>

      <!-- 第三行：质量问题排查和记录 -->
      <div class="status-section">
        <div class="status-item">
          <svg-icon name="correction" color="#fff" style="width: 28px;height: 28px;margin-top: 4px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.rectificationRate') }}: <span class="recordNum"
                style="font-size: 20px;">{{ qualityData.整改率 || 0
                }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.percent') }}</span>
            </div>
          </div>
        </div>
        <div class="status-item">
          <svg-icon name="timelyRectification" color="#fff" style="width: 28px;height: 28px;margin-top: 4px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.timelyRectificationRate') }}: <span
                class="recordNum" style="font-size: 20px;">{{ qualityData.整改及时率 || 0
                }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.percent') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 第四行：合格/待核验/待复查/待整改 -->
      <div class="issue-section">
        <div class="issue-bg"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.qualified') }}</div>
            <div class="issue-value "> <span class="blueFont">{{ qualityData.合格 || 0 }}</span> {{
              t('projectDetail.units.projects') }}</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingVerification') }}</div>
            <div class="issue-value "> <span class="yellowFont">{{ qualityData.待核验 || 0 }}</span> {{
              t('projectDetail.units.projects') }}</div>
          </div>
        </div>
        <div class="divider-line"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingReview') }}</div>
            <div class="issue-value "> <span class="yellowFont">{{ qualityData.待复查 || 0 }}</span> {{
              t('projectDetail.units.projects') }}</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingRectification') }}</div>
            <div class="issue-value "> <span class="redFont">{{ qualityData.待整改 || 0 }}</span>{{
              t('projectDetail.units.projects') }}</div>
          </div>
        </div>
      </div>

      <!-- 第五行：质量问题统计图表 (组合图表) -->
      <div class="chart-section">
        <div class="chart">
          <CombinedChart :barData="qualityBarData" :lineData="qualityLineData" :xData="xData">
          </CombinedChart>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { nextTick, ref, watch, markRaw, onMounted, computed } from "vue";
import { getImg, formatNumber } from "@/utils/method";
import * as echarts from "echarts";
import PieChart from "./PieChart.vue";
import BarChart from "./BarChart.vue";
import WaterBall from "./WaterBall.vue";
import LineChart from "./LineChart.vue";
import RotatingCircle from "@/components/RotatingCircle.vue";
import TitleBar from "@/components/TitleBar.vue";
import CombinedChart from "./CombinedChart.vue";
import axios from "axios";
import request from "@/utils/request";
import { useI18n } from 'vue-i18n';
import SvgIcon from '@/components/SvgIcon.vue';

const { t } = useI18n();

const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({}),
  },
});

const title = ref(t('projectDetail.overview.projectTitle'));
const safetyData = ref({
  排查记录: 0,
  隐患记录: 0,
  已销项: 0,
  待复查: 0,
  待整改: 0,
  超期: 0,
  "1月": "0:0.00",
  "2月": "0:0.00",
  "3月": "0:0.00",
  "4月": "0:0.00",
  "5月": "0:0.00"
});

// 新增质量数据
const qualityData = ref({
  整改率: "0",
  整改及时率: "0",
  合格: 0,
  待复查: 0,
  待核验: 0,
  待整改: 0,
  "1月": "0:0.00",
  "2月": "0:0.00",
  "3月": "0:0.00",
  "4月": "0:0.00",
  "5月": "0:0.00"
});

// 计算用于安全隐患图表的数据
const safetyChartData = computed(() => {
  const months = ["1月", "2月", "3月", "4月", "5月"];
  const chartData = {
    name: "隐患数量",
    data: [],
    percentages: []
  };

  months.forEach(month => {
    if (safetyData.value[month]) {
      // 从"数值:百分比"格式中提取数值和百分比
      const parts = safetyData.value[month].split(':');
      const value = parseInt(parts[0]) || 0;
      const percentage = parts.length > 1 ? parts[1] : "0.00";

      chartData.data.push(value);
      chartData.percentages.push(percentage);
    } else {
      chartData.data.push(0);
      chartData.percentages.push("0.00");
    }
  });

  return chartData;
});

// 格式化项目数据
const formatProjectData = (data) => {
  if (data) {
    title.value = data.项目名称;
    // 可以在这里处理其他数据
  }
};

// 获取安全隐患数据
const fetchSafetyData = async () => {
  try {
    // 检查是否为国际版
    const isInternational = !localStorage.getItem("isChina");
    if (isInternational) {
      // 国际版使用模拟数据
      safetyData.value = {
        排查记录: 76,
        隐患记录: 25,
        已销项: 12,
        待复查: 5,
        待整改: 6,
        超期: 2,
        "1月": "5:45.00",
        "2月": "8:60.00",
        "3月": "6:55.00",
        "4月": "4:40.00",
        "5月": "2:20.00"
      };
      return;
    }

    // 国内版请求接口
    // 从sessionStorage获取项目信息
    const projectInfo = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    const projectId = projectInfo.code || "";

    if (!projectId) {
      console.error("无法获取项目ID");
      return;
    }
    // 直接请求安全隐患数据接口
    const response = await request.get('/globalManage/zjmanage/largescreen/getSafety', {
      params: { id: projectId },
    });

    if (response.data.code === 0 && response.data.data) {
      safetyData.value = response.data.data;

      // 确保超期字段为数字
      if (typeof safetyData.value.超期 === 'string') {
        safetyData.value.超期 = parseInt(safetyData.value.超期) || 0;
      }
    } else {
      console.error("获取安全隐患数据失败:", response.data.msg);
    }
  } catch (error) {
    console.error("请求安全隐患数据出错:", error);
  }
};

// 新增获取质量数据函数
const fetchQualityData = async () => {
  try {
    // 检查是否为国际版
    const isInternational = !localStorage.getItem("isChina");
    if (isInternational) {
      // 国际版使用模拟数据
      qualityData.value = {
        整改率: "85",
        整改及时率: "90",
        合格: 56,
        待复查: 10,
        待核验: 15,
        待整改: 8,
        "1月": "10:85.00",
        "2月": "12:90.00",
        "3月": "8:88.00",
        "4月": "6:92.00",
        "5月": "4:95.00"
      };
      return;
    }

    // 国内版请求接口
    // 从sessionStorage获取项目信息
    const projectInfo = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    const projectId = projectInfo.code || "";

    if (!projectId) {
      console.error("无法获取项目ID");
      return;
    }
    // 请求质量数据接口
    const response = await request.get('/globalManage/zjmanage/largescreen/getQuality', {
      params: { id: projectId },
    });

    if (response.data.code === 0 && response.data.data) {
      qualityData.value = response.data.data;

      // 确保数值字段为数字类型
      ['合格', '待复查', '待核验', '待整改'].forEach(field => {
        if (typeof qualityData.value[field] === 'string') {
          qualityData.value[field] = parseInt(qualityData.value[field]) || 0;
        }
      });

      // 确保百分比字段为字符串类型
      ['整改率', '整改及时率'].forEach(field => {
        if (typeof qualityData.value[field] === 'number') {
          qualityData.value[field] = qualityData.value[field].toString();
        }
      });
    } else {
      console.error("获取质量数据失败:", response.data.msg);
    }
  } catch (error) {
    console.error("请求质量数据出错:", error);
  }
};

onMounted(() => {
  formatProjectData(props.projectData);
  fetchSafetyData();
  fetchQualityData(); // 新增质量数据获取
});

watch(
  () => props.projectData,
  (newData) => {
    formatProjectData(newData);
  }
);

const list1 = ref([
  {
    id: 1,
    title: "排查记录",
    num: 76,
    icon: getImg("project/icon7.png"),
  },
  {
    id: 2,
    title: "隐患记录",
    num: 6666,
    icon: getImg("project/icon8.png"),
  },
]);

const pieData1 = ref([
  {
    value: 1914,
    name: "已销项",
    itemStyle: { color: "rgba(0,255,170,1)" },
  },
  {
    value: 1601,
    name: "待复查",
    itemStyle: { color: "rgba(0,255,255, 1)" },
  },
  {
    value: 1537,
    name: "待整改",
    itemStyle: { color: "rgba(247,147,30, 1)" },
  },
  {
    value: 1007,
    name: "超期",
    itemStyle: { color: "rgba(237,28,36, 1)" },
  },
]);

const pieData2 = ref([
  {
    value: 56,
    name: "合格",
    itemStyle: { color: "rgba(0,255,170,1)" },
  },
  {
    value: 79,
    name: "待核验",
    itemStyle: { color: "rgba(0,255,255, 1)" },
  },
  {
    value: 49,
    name: "待复查",
    itemStyle: { color: "rgba(247,147,30, 1)" },
  },
  {
    value: 27,
    name: "待整改",
    itemStyle: { color: "rgba(237,28,36, 1)" },
  },
]);

const legend = ref(["折旧金额"]);
const xData = ref(["1月", "2月", "3月", "4月", "5月"]);
const chartDate = ref([
  {
    name: "折旧金额",
    itemStyle: { color: "rgba(5,85,163, 1)" },
    data: [60, 85, 56, 33, 20],
  },
]);

// 质量问题柱状图数据 - 动态计算
const qualityBarData = computed(() => {
  const months = ["1月", "2月", "3月", "4月", "5月"];
  const chartData = {
    name: "问题数量",
    itemStyle: { color: "rgba(233,54,40, 1)" },
    data: []
  };

  months.forEach(month => {
    if (qualityData.value[month]) {
      // 从"数值:百分比"格式中提取数值
      const parts = qualityData.value[month].split(':');
      const value = parseInt(parts[0]) || 0;
      chartData.data.push(value);
    } else {
      chartData.data.push(0);
    }
  });

  return chartData;
});

// 质量问题整改百分率折线图数据 - 动态计算
const qualityLineData = computed(() => {
  const months = ["1月", "2月", "3月", "4月", "5月"];
  const chartData = {
    name: "整改率",
    itemStyle: { color: "rgba(0,152,101, 1)" },
    data: []
  };

  months.forEach(month => {
    if (qualityData.value[month]) {
      // 从"数值:百分比"格式中提取百分比
      const parts = qualityData.value[month].split(':');
      const percentage = parts.length > 1 ? parseFloat(parts[1]) : 0;
      chartData.data.push(percentage);
    } else {
      chartData.data.push(0);
    }
  });

  return chartData;
});
</script>

<style lang="scss" scoped>
.left-content {
  width: 860px;
  height: 100%;
  padding: 100px 20px 20px 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: linear-gradient(to right, #0D121C 0%, #0f141f 70%, transparent 100%);

  .left-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    z-index: 2;
    margin-bottom: 10px;
  }
}

/* 项目标题样式 */
.project-title {
  width: 400px;
  height: 60px;
  background: url("@/assets/images/map/title.png") no-repeat;
  background-size: 100% 100%;
  font-family: PangMenZhengDao, PangMenZhengDao;
  font-weight: 400;
  font-size: 20px;
  color: #F2F3FF;
  line-height: 24px;
  letter-spacing: 1px;
  text-shadow: 0px 0px 10px #0094FF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  padding-left: 30px;
}

/* 信息部分标题样式 */
.info-section {
  width: 400px;
  height: 35px;
  margin: 25px 0 30px 0;

  .info-section-bg {
    width: 400px;
    height: 40px;
    background: url("@/assets/images/map/projectInfo.png") no-repeat;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 40px;
    font-family: PangMenZhengDao, PangMenZhengDao;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 23px;
    letter-spacing: 2px;
    text-align: justified;
    font-style: normal;
    text-transform: none;
  }
}

/* 状态信息样式 */
.status-section {
  width: 400px;
  height: 42px;
  display: flex;
  margin-bottom: 20px;
  background: url("@/assets/images/map/safetySeconed.png") no-repeat;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 0 20px;

  .status-item {
    display: flex;
    align-items: center;
    width: 50%;

    .status-icon {
      width: 80px;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .status-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 10px;

      .status-label {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #DBE9FF;
        line-height: 19px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        white-space: nowrap;
      }

      .status-value {
        display: flex;
        align-items: baseline;
        font-family: TCloudNumber;
        font-size: 28px;
        font-weight: bold;
        background-image: linear-gradient(to bottom,
            #9AC4FF 0%,
            #FFFFFF 62%,
            #9AC4FF 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;

        .unit {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 16px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}

/* 问题状态区域 */
.issue-section {
  width: 400px;
  height: 120px;
  position: relative;
  background: url("@/assets/images/map/fourBg.png") no-repeat;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .issue-row {
    display: flex;
    height: 50%;
    padding: 0 20px;
    height: 50px;

    .issue-item {
      width: 50%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 20px;

      .issue-label {
        font-size: 14px;
        color: #ffffff;
      }
    }
  }

  .divider-line {
    width: 90%;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 auto;
  }
}

/* 图表区域 */
.chart-section {
  width: 400px;
  height: 180px;

  .chart-title {
    width: 100%;
    height: 20px;
    background: url('@/assets/images/project/sTitle.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding-left: 20px;
    font-size: 14px;
    line-height: 17px;
    margin-top: 10px;
    margin-bottom: 10px;
    font-weight: 300;
    color: #ffffff;
  }

  .chart {
    width: 100%;
    height: 100%;
  }
}

.recordNum {
  font-family: 'TCloudNumber';
  font-size: 22px;
  font-weight: bold;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 62%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.recordNumRed {
  font-family: 'TCloudNumber';
  font-size: 22px;
  background-image: linear-gradient(to bottom,
      #FE6263 0%,
      #FFFFFF 50%,
      #FE6263 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: bold;
}

.blueFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 20px;
  color: #79BFFF;
  line-height: 28px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.redFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 20px;
  color: #FF6262;
  line-height: 28px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.yellowFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 20px;
  color: #FCD494;
  line-height: 28px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
</style>
