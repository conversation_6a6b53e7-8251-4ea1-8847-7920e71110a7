<template>
  <div
    class="project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between">
    <div class="leftList" :class="{ 'leftList-china': isChina }">
      <div v-if="infoLists.length === 0" class="items" v-for="(item, index) in infoList" :key="index">
        <div class="item-img">
          <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
              style="width: 50px;height: 50px;" /></RotatingCircle>
        </div>
        <div class="item-content">
          <div class="item-title">{{ item.name }}</div>
          <div class="item-amount">
            <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
              <count-to v-if="item.needCount" :start-val="0" :end-val="item.amount" :duration="2000" :decimals="2"
                :autoplay="true" separator="," />
              <template v-else>{{ item.amount }}</template>
            </span><span class="bgfont">{{ item.unit }}</span>
          </div>
        </div>
      </div>
      <div v-if="infoLists.length > 0" class="items" v-for="(item, index) in infoLists" :key="index">
        <div class="item-img">
          <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
              style="width: 50px;height: 50px;" /></RotatingCircle>
        </div>
        <div class="item-content">
          <div class="item-title">{{ item.name }}</div>
          <div class="item-amount">
            <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
              <count-to v-if="item.needCount" :start-val="0" :end-val="item.amount" :duration="2000" :decimals="2"
                :autoplay="true" separator="," />
              <template v-else>{{ item.amount }}</template>
            </span><span class="bgfont">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="right-list-container" v-if="rightList.length > 0">
      <div class="list-content">
        <div
          v-for="(item, index) in rightList"
          :key="item.data.项目ID || item.data.营地id"
          class="list-item"
          @click="onRightListItemClick(item)"
          @mouseenter="handleItemHover(item)"
          @mouseleave="clearHighlight"
        >
          <span class="item-index">{{ index + 1 }}</span>
          <span class="item-name">{{ item.name }}</span>
        </div>
      </div>
    </div> -->

    <div v-if="!isLoading" class="back-button" @click="back">
            <img src="@/assets/images/header/back.png" alt="" />
            <span>返回</span>
        </div>
    <!-- <img v-if="!isLoading" class="pointer-events-auto cursor-pointer absolute top-[30px] left-[60px]"
      src="@/assets/images/xiangmu/back.png" alt="" srcset="" @click="back" /> -->
    <!-- <button v-if="!isLoading" @click="toggleLanguage"
      class="pointer-events-auto cursor-pointer absolute top-[30px] right-[60px] px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
      {{ locale === 'zh' ? 'EN' : '中' }}
    </button> -->
    <!-- <div
      class="absolute w-full flex justify-center font-family-youshebiaotihei tracking-[8px] text-[30px] title mt-[70px]">
      {{ country }}
    </div> -->
  </div>
</template>
<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { CountTo } from 'vue3-count-to';
import RotatingCircle from '@/components/RotatingCircle.vue';
import SvgIcon from '@/components/SvgIcon.vue';
import request from "@/utils/request";

const router = useRouter();
const route = useRoute();
const iframe = ref(document.getElementById("iframe"));
const { t, locale } = useI18n();

const rightList = ref([]);

const isChina = ref(localStorage.getItem("isChina") == "1" ? true : false);

const country = ref(sessionStorage.getItem("clickCountry") || "中国");
const countryData = ref(0);
const projectApiData = ref([]);
const campApiData = ref([]);
const gcksr = ref(0);
const htze = ref(0);
const bnmb = ref(0);
const renshu = ref(0);
const camps = ref(0);
const gnrs = ref(0);
const hwrs = ref(0);

const zsyg = ref(0);
const lwpq = ref(0);
const qtyg = ref(0);
const hzdw = ref(0);

const pieData = ref([]);

const lastBackTime = ref(0);
let throttleTimer = ref(null);

const isLoading = ref(true);

const infoList = computed(() => [
  { id: "projects", name: t('country.projectsUnderConstruction'), amount: 0, unit: t('country.units.projects'), svgName: "home-2", needCount: false },
  { id: "camps", name: t('country.campCount'), amount: 0, unit: t('country.units.camps'), svgName: "home-1", needCount: false },
  { id: "htze", name: t('country.contractAmount'), amount: 0, unit: "", svgName: "", needCount: true },
  { id: "bnmb", name: t('country.cumulativeOutput'), amount: 0, unit: "", svgName: "", needCount: true },
  { id: "gcksr", name: t('country.engineeringRevenue'), amount: 0, unit: "", svgName: "", needCount: true }
]);

// 新增：安全数字转换函数，防止NaN
function safeNumber(val) {
  const n = Number(val);
  return isNaN(n) ? 0 : n;
}

// 语言切换函数
const toggleLanguage = () => {
  const currentLang = locale.value;
  const newLang = currentLang === 'zh' ? 'en' : 'zh';
  locale.value = newLang;
  localStorage.setItem('language', newLang);
};

// 消息处理函数
const handleMessage = (e) => {
  console.log(e.data, '这是country');

  if (e.data.eve === 'initDemoInfo' && e.data.title) {
    const level = e.data.level || 'district';
    getInfoByLevel(level, e.data.title);
    return;
  }
  if (e.data.eve === 'cancle') {
    rightList.value = [];
  }

  // 处理模型点击跳转逻辑
  if (e.data.eve === 'clickDemo') {
    try {
      // 验证必要参数
      if (!e.data.type || !e.data.code || !e.data.name) {
        console.error('模型点击数据不完整:', e.data);
        return;
      }

      if (e.data.type === "project") {
        // 增强项目数据处理：自动补充isGw参数
        const projectData = {
          ...e.data,
          // 如果没有isGw参数，根据当前环境自动判断
          isGw: e.data.isGw || String(!isChina.value)
        };
        
        console.log('跳转到项目详情:', projectData);
        sessionStorage.setItem("clickProject", JSON.stringify(projectData));
        router.push({
          path: "/projectDetail/index"
        });
      } else if (e.data.type === 'yd') {
        // 增强营地数据处理：自动补充isGw参数
        const campData = {
          ...e.data,
          // 营地通常都是国外的，默认设置为true
          isGw: e.data.isGw || "true"
        };
        
        console.log('跳转到营地详情:', campData);
        sessionStorage.setItem("clickProject", JSON.stringify(campData));
        router.push({
          path: "/yingdi/index"
        });
      } else {
        console.warn('未知的点击类型:', e.data.type);
      }
    } catch (error) {
      console.error('处理模型点击事件失败:', error);
    }
  }
  if (e.data.level) {
    console.log('执行广播函数');

    // 保存level和title到sessionStorage，保证在同一会话中菜单切换后数据不丢失
    sessionStorage.setItem("countryLevel", e.data.level);
    sessionStorage.setItem("countryTitle", e.data.title);
    sessionStorage.setItem("clickCountry", e.data.title);

    // 根据level获取不同级别的信息
    getInfoByLevel(e.data.level, e.data.title);
  }
};

// 处理路由传递过来的数据
const processRouteData = () => {
  // 从sessionStorage获取数据
  const countryLevel = sessionStorage.getItem("countryLevel");
  const countryTitle = sessionStorage.getItem("countryTitle");

  if (countryTitle && countryTitle !== "中国" && countryTitle !== "earth") {
    country.value = countryTitle;
    sessionStorage.setItem("clickCountry", countryTitle);
  }

  if (countryLevel && countryTitle) {
    getInfoByLevel(countryLevel, countryTitle);
  } else if (sessionStorage.getItem("clickCountry") && sessionStorage.getItem("clickCountry") !== "中国") {
    // 如果有保存的国家/地区信息，但没有明确的级别信息，尝试根据国家/地区名称加载数据
    const savedCountry = sessionStorage.getItem("clickCountry");
    country.value = savedCountry;
    // 根据保存的国家/地区名称确定级别并加载数据
    determineAndLoadData(savedCountry);
  }
};

// 根据区域名称判断级别并加载数据
const determineAndLoadData = (areaName) => {
  if (!areaName || areaName === "中国" || areaName === "earth") {
    return;
  }

  // 这里可以添加逻辑来确定区域的级别
  // 简单示例：可以根据区域名称中是否包含"省"、"市"、"区"等关键字判断
  let level = 'country'; // 默认为国家级别

  if (areaName.includes('省') || /^(内蒙古|广西|西藏|宁夏|新疆)/.test(areaName)) {
    level = 'province';
  } else if (areaName.includes('市') || areaName.includes('自治州')) {
    level = 'city';
  } else if (areaName.includes('区') || areaName.includes('县') || areaName.includes('旗')) {
    level = 'district';
  }

  // 保存确定的级别到sessionStorage
  sessionStorage.setItem("countryLevel", level);

  // 加载数据
  getInfoByLevel(level, areaName);
};

onMounted(() => {
  iframe.value = document.getElementById("iframe");
  isLoading.value = true;

  // 处理保存的数据
  processRouteData();

  // 如果没有需要加载的数据，可以在这里直接设置加载完成
  if (!sessionStorage.getItem("countryLevel") &&
    (!sessionStorage.getItem("clickCountry") || sessionStorage.getItem("clickCountry") === "中国")) {
    isLoading.value = false;
  }

  // 然后添加消息监听
  window.addEventListener("message", handleMessage);
});

onBeforeUnmount(() => {
  // 移除消息监听器，防止内存泄漏
  window.removeEventListener("message", handleMessage);
  if (hoverTimer) {
    clearTimeout(hoverTimer);
  }
});

const infoLists = ref([]);
const getInfoByLevel = async (dataLevel, dataTitle) => {
  console.log(dataLevel, dataTitle);
  try {
    isLoading.value = true;
    let res;
    const level = dataLevel;

    if (level === 'country') {
      res = await request.get('/globalManage/zjmanage/largescreen/getForeignInfoByCountry', {
        params: { gb: dataTitle }
      });
      console.log('执行城市方法', level, dataTitle);

    } else if (level === 'province') {
      res = await request.get('/globalManage/zjmanage/largescreen/getInnerProviceCityInfo', {
        params: { province: dataTitle }
      });
    } else if (level === 'city') {
      res = await request.get('/globalManage/zjmanage/largescreen/getInnerProviceCityDistrictInfo', {
        params: { city: dataTitle }
      });
    } else if (level === 'district') {
      res = await request.get('/globalManage/zjmanage/largescreen/getInnerDistrictBaseInfo', {
        params: { district: dataTitle }
      });
    }
    if (res.code === 0 && res.data && res.data.length > 0) {
      const data = res.data[0];
      console.log(data, 'data');

      // 更新名称
      country.value = dataTitle;
      // 更新数据
      countryData.value = safeNumber(data.在建项目总数);
      gcksr.value = safeNumber(data.工程款收入);
      htze.value = safeNumber(data.在建项目合同总额);
      bnmb.value = safeNumber(data.累计产值);
      camps.value = safeNumber(data.营地数量);
      renshu.value = safeNumber(data.项目总人数);
      gnrs.value = safeNumber(data.中方);
      hwrs.value = safeNumber(data.外籍);

      // 添加对国内数据的处理
      zsyg.value = safeNumber(data.正式员工);
      lwpq.value = safeNumber(data.劳务派遣);
      qtyg.value = safeNumber(data.其他用工);
      hzdw.value = safeNumber(data.合作单位);

      // 如果接口返回了"其他形式用工"总数，直接使用这个值
      if (data.其他形式用工) {
        lwpq.value = 0;
        qtyg.value = 0;
        hzdw.value = safeNumber(data.其他形式用工);
      }

      // 创建新的数组来触发响应式更新
      const newInfoList = infoList.value.map(item => {
        switch (item.id) {
          case "projects":
            return { ...item, amount: countryData.value };
          case "camps":
            return { ...item, amount: camps.value };
          case "gcksr":
            return {
              ...item,
              amount: gcksr.value,
              unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'),
              svgName: "home-5-" + (isChina.value ? "r" : "l")
            };
          case "htze":
            return {
              ...item,
              amount: htze.value,
              unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'),
              svgName: "home-3-" + (isChina.value ? "r" : "l")
            };
          case "bnmb":
            return {
              ...item,
              amount: bnmb.value,
              unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'),
              svgName: "home-4-" + (isChina.value ? "r" : "l")
            };
          default:
            return item;
        }
      }).filter(item => {
        // 对于国际国家，显示所有指标；对于国内，营地数量可能需要过滤
        if (isChina.value && item.id === 'camps') {
          return false; // 国内不显示营地数量
        }
        return true;
      }).sort((a, b) => {
        // 如果是国内项目，将工程款收入(gcksr)排到最后
        if (isChina.value) {
          if (a.id === 'gcksr') return 1;
          if (b.id === 'gcksr') return -1;
        }
        return 0;
      });

      infoLists.value = newInfoList;
      // 强制更新视图
      nextTick(() => {
        infoLists.value = newInfoList;
        console.log('数据已更新:', infoList.value);
        isLoading.value = false;
      });
      fetchRightList(dataTitle);
    } else {
      isLoading.value = false;
    }
  } catch (error) {
    console.error('获取信息失败:', error);
    isLoading.value = false;
  }
};

let hoverTimer = null;
const handleItemHover = (item) => {
  if (hoverTimer) {
    clearTimeout(hoverTimer);
  }
  hoverTimer = setTimeout(() => {
    if (iframe.value && iframe.value.contentWindow) {
      iframe.value.contentWindow.postMessage(
        {
          eve: "highLigtDemo",
          name: item.name
        },
        "*"
      );
    }
  }, 300);
};

const clearHighlight = () => {
  if (hoverTimer) {
    clearTimeout(hoverTimer);
  }
  if (iframe.value && iframe.value.contentWindow) {
    iframe.value.contentWindow.postMessage(
      { eve: "highLigtDemo",
        name: '' },
      "*"
    );
  }
};

const onRightListItemClick = (item) => {
  const targetPath = item.type === 'project' ? '/projectDetail/index' : '/yingdi/index';
  const payload = {
    eve: "clickDemo",
    type: item.type,
    code: item.type === 'project' ? (item.data.ID || item.data.项目ID) : item.data.营地id,
    name: item.name,
    isGw: String(!isChina.value)
  };
  
  sessionStorage.setItem("clickProject", JSON.stringify(payload));
  router.push({ path: targetPath });
};

let backTimer = null;

const back = () => {
  if (backTimer) {
    clearTimeout(backTimer); // 如果已经设置了延迟，则清除之前的定时器
  }

  // 设置新的定时器，只会执行最后一次点击
  backTimer = setTimeout(() => {
    iframe.value.contentWindow.postMessage(
      { eve: "cancle" },
      "*"
    );

    // 如果当前路径已经是/project/index，则不进行跳转
    if (router.currentRoute.value.path !== '/project/index') {
      router.replace("/project/index");
    }
    backTimer = null;
  }, 1200);
};

const fetchRightList = async (areaName) => {
  try {
    const [projectRes, campRes, projectRes2] = await Promise.all([
      request.get('/globalManage/zjmanage/largescreen/getXmxxV2', {
        params: { gb: areaName }
      }),
      request.get('/globalManage/zjmanage/largescreen/getYdxxV2', {
        params: { gb: areaName }
      }),
      // 国内项目，只有区县有项目
      sessionStorage.getItem("countryLevel") === 'district' ? request.get('/globalManage/zjmanage/largescreen/getXmjbxx', {
        params: { district: areaName }
      }) : null
    ]);

    const projects = (projectRes.code === 0 && projectRes.data)
      ? projectRes.data.map(p => ({ name: p.项目名称, type: 'project', data: p }))
      : [];

    const camps = (campRes.code === 0 && campRes.data)
      ? campRes.data.map(c => ({ name: c.营地名称, type: 'yd', data: c }))
      : [];

    const projects2 = (projectRes2 && projectRes2.code === 0 && projectRes2.data)
      ? projectRes2.data.map(p => ({ name: p.项目名称, type: 'project', data: p }))
      : [];

    rightList.value = [...projects, ...camps, ...projects2];
  } catch (error) {
    console.error('获取项目和营地列表失败:', error);
    rightList.value = [];
  }
};

</script>
<style scoped lang="scss">
.project-container {
  pointer-events: none;
  z-index: 2;

  .leftList {
    display: flex;
    flex-direction: column;
    position: relative;
    gap: 20px;
    top: 40px;
    z-index: 99;

    &.leftList-china {
      top: 0;
      gap: 60px;
      margin: auto 0;
    }

    .item-img {
      position: relative;
      top: -16px;
    }

    .items {
      display: flex;
      gap: 20px;
      position: relative;
      margin-bottom: 20px;

      .item-amount {
        font-family: "xiaoweiLogo";
      }
    }
  }
}

.title {
  text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.8);
}

.bgfont {
  font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.bg-gradient {
  background: linear-gradient(180deg, rgba(0, 132, 255, 0) 0%, #0084FF 50%, rgba(0, 132, 255, 0) 100%);
}
.back-button {
    position: absolute;
    top: 30px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
}

.right-list-container {
  position: absolute;
  right: 20px;
  top: 120px;
  width: 350px;
  height: calc(100% - 140px);
  // background: rgba(10, 24, 60, 0.5);
  // border: 1px solid rgba(0, 132, 255, 0.5);
  border-radius: 8px;
  color: #fff;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  z-index: 99;

  .list-header {
    background: url('@/assets/images/map/controlBg.png') no-repeat;
    background-size: 100% 100%;
    text-align: center;
    font-size: 20px;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    color: #EEF4FF;
    flex-shrink: 0;
  }

  .list-content {
    padding: 10px;
    overflow-y: auto;
    flex-grow: 1;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #0084ff;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .list-item {
    display: flex;
    align-items: center;
    padding: 10px 5px;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 132, 255, 0.2);
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(0, 132, 255, 0.1);
    }

    .item-index {
      margin-right: 15px;
      font-size: 14px;
      color: #9ac4ff;
      width: 20px;
      text-align: center;
    }

    .item-name {
      font-size: 16px;
      flex-grow: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
