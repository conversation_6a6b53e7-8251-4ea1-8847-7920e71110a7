<template>
  <div class="video-grid">
    <div class="video-row" v-for="(row, rowIndex) in deviceRows" :key="rowIndex">
      <div class="video-item" v-for="(device, colIndex) in row" :key="device ? device.nvrSerialNo + '-' + device.cameraChannel : colIndex">
        <div class="monitor-bg" v-if="device">
          <div class="monitor-name" :title="device.projectName">
            {{ device.projectName || '公建中心项目' + (rowIndex * 4 + colIndex + 1) }}
          </div>
          <!-- 公建中心视频播放器 -->
          <div :id="'videoPlayer-gjzx-' + (rowIndex * 4 + colIndex)" class="video-player"></div>
        </div>
        <div class="monitor-bg empty-monitor" v-else>
          <div class="monitor-name">等待项目</div>
          <div class="empty-monitor-placeholder">
            <div class="placeholder-icon">📷</div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="deviceList.length === 0 && !loading" class="empty-state">
      <div class="empty-text">暂无数据</div>
    </div>
  </div> 
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, computed } from 'vue'
import axios from 'axios';

const props = defineProps({
  deviceList: {
    type: Array,
    default: () => []
  }
})
const myPlayer = ref([])
const cloudToken = ref('')
const loading = ref(false)

// 获取 cloudToken
const getCloudToken = async () => {
  try {
    // loading.value = true
    // 第一步：获取 access_token
    const client_id = 'd1b4e2d1e64c4a3eaf6a288d6e4215f1'
    const client_secret = '723f7d81ef8b4f70a236ff6514bb458e'
    const grant_type = 'client_credentials'
    const res1 = await axios.post(`/oauth/token?grant_type=${grant_type}&client_id=${client_id}&client_secret=${client_secret}`)
    const access_token = res1?.data?.access_token
    if (!access_token) return
    // 第二步：用 access_token 获取 cloudToken
    const res2 = await axios.get(`/v1/ezviz/account/info`, {
      headers: { Authorization: 'Bearer ' + access_token }
    })
    const token = res2?.data?.data?.token
    if (token) {
      cloudToken.value = token
      localStorage.setItem('cloudToken', token)
      initAllPlayers()
    }
  } catch (e) {
    cloudToken.value = ''
  } finally {
    // loading.value = false
  }
}

// 按4个一行分组，最多16个
const deviceRows = computed(() => {
  const perRow = 4
  const maxDevices = 16
  const arr = []
  const list = props.deviceList.slice(0, maxDevices)
  for (let i = 0; i < maxDevices; i += perRow) {
    arr.push(list.slice(i, i + perRow))
  }
  // 补空
  for (let i = 0; i < arr.length; i++) {
    while (arr[i].length < perRow) {
      arr[i].push(null)
    }
  }
  return arr
})

// 初始化所有播放器
const initAllPlayers = () => {
  props.deviceList.slice(0, 16).forEach((item, idx) => {
    setUrl(item, idx)
  })
}

// setUrl实现播放器初始化
envCheckEZUIKit()
function envCheckEZUIKit() {
  if (typeof window !== 'undefined' && !window.EZUIKit) {
    // eslint-disable-next-line no-console
    console.warn('EZUIKit 未全局引入，播放器无法初始化')
  }
}
const setUrl = (item, index) => {
  if (!item) return
  // 销毁旧播放器
  if (myPlayer.value[index]) {
    myPlayer.value[index].destroy && myPlayer.value[index].destroy()
    myPlayer.value[index] = null
  }
  // 获取token
  const accessToken = cloudToken.value || localStorage.getItem('cloudToken') || ''
  
  if (!window.EZUIKit) return
  // EZUIKit主题配置
  const ezuikitTheme = {
      autoFocus: 5,
      poster: "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
      header: {
        color: "#1890ff",
        activeColor: "#FFFFFF",
        backgroundColor: "#000000",
        btnList: []
      },
      footer: {
        color: "#FFFFFF",
        activeColor: "#1890FF",
        backgroundColor: "#00000051",
        btnList: [
          {
            iconId: "play",
            part: "left",
            defaultActive: 1,
            memo: "播放",
            isrender: 1,
          },
          {
            iconId: "capturePicture",
            part: "left",
            defaultActive: 0,
            memo: "截屏按钮",
            isrender: 1,
          },
          {
            iconId: "sound",
            part: "left",
            defaultActive: 0,
            memo: "声音按钮",
            isrender: 1,
          },
          {
            iconId: "recordvideo",
            part: "left",
            defaultActive: 0,
            memo: "录制按钮",
            isrender: 1,
          },
          {
            iconId: "expend",
            part: "right",
            defaultActive: 0,
            memo: "全局全屏按钮",
            isrender: 1,
          },
        ],
      },
    };
  myPlayer.value[index] = new window.EZUIKit.EZUIKitPlayer({
    staticPath: '/ezuikit_static/v65',
    id: 'videoPlayer-gjzx-' + index,
    accessToken: accessToken,
    url: `ezopen://open.ys7.com/${item.nvrSerialNo}/${item.cameraChannel}.hd.live`,
    audio: false,
    width: 325,
    height: 176,
    useHardDev: false,
    themeData: ezuikitTheme,
  })
}

// 状态样式和文本
const getStatusClass = (status) => {
  switch (status) {
    case '1': return 'online';
    case '0': return 'offline';
    case '-1': return 'error';
    default: return 'unknown';
  }
}
const getStatusText = (status) => {
  switch (status) {
    case '1': return '在线';
    case '0': return '离线';
    case '-1': return '异常';
    default: return '未知';
  }
}

onMounted(() => {
  getCloudToken()
  
})

onBeforeUnmount(() => {
  myPlayer.value.forEach(player => {
    player && player.destroy && player.destroy()
  })
})
</script>

<style scoped>
.video-grid {
  width: 100%;
  height: calc(100% - 80px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.video-row {
  min-height: 120px;
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.video-item {
  flex: 1;
  aspect-ratio: 16/9;
  min-height: 140px;
  border-radius: 8px;
  overflow: hidden;
}

.monitor-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  transition: all 0.3s ease;
  background: #0c2538;
  border: 2px solid #636771;
}

.monitor-bg:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.monitor-name {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  height: auto;
  padding: 8px 12px;
  box-sizing: border-box;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 12px;
  color: #00FFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.monitor-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-size: 10px;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
  position: absolute;
  bottom: 8px;
  left: 0;
  z-index: 6;
}

.device-serial,
.channel-no {
  background: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.8);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
}

.status-indicator {
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 500;
}

.status-indicator.online {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.status-indicator.offline {
  background: rgba(255, 152, 0, 0.3);
  color: #FF9800;
}

.status-indicator.error {
  background: rgba(244, 67, 54, 0.3);
  color: #F44336;
}

.status-indicator.unknown {
  background: rgba(158, 158, 158, 0.3);
  color: #9E9E9E;
}

.empty-monitor {
  opacity: 0.3;
  pointer-events: none;
}

.empty-monitor-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.placeholder-icon {
  font-size: 30px;
  opacity: 0.5;
}
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
}
.empty-text {
  font-size: 18px;
  font-weight: 500;
}
.loading-overlay {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  z-index: 10;
}
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
}
.loading-text {
  margin-top: 15px;
  color: #00FFFF;
  font-size: 16px;
  font-weight: 500;
}
</style>
