/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Circular: typeof import('./src/components/Circular.vue')['default']
    DeviceSelector: typeof import('./src/components/DeviceSelector.vue')['default']
    Earth3D: typeof import('./src/components/Earth3D/index.vue')['default']
    EarthZT: typeof import('./src/components/EarthZT/index.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    HazardDoughnutChart: typeof import('./src/components/HazardDoughnutChart.vue')['default']
    Index1: typeof import('./src/components/Earth3D/index1.vue')['default']
    Index2: typeof import('./src/components/Earth3D/index2.vue')['default']
    Index3: typeof import('./src/components/Earth3D/index3.vue')['default']
    LanguageSwitcher: typeof import('./src/components/LanguageSwitcher.vue')['default']
    LineChart: typeof import('./src/components/LineChart.vue')['default']
    PersonnelRingChart: typeof import('./src/components/PersonnelRingChart.vue')['default']
    Pie3DChart: typeof import('./src/components/Pie3DChart.vue')['default']
    Pie3DChart2: typeof import('./src/components/Pie3DChart2.vue')['default']
    RotatingCircle: typeof import('./src/components/RotatingCircle.vue')['default']
    RotatingCircleGreen: typeof import('./src/components/RotatingCircleGreen.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SafetyRingChart: typeof import('./src/components/SafetyRingChart.vue')['default']
    StaffChart: typeof import('./src/components/StaffChart.vue')['default']
    StaffChart2: typeof import('./src/components/StaffChart2.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
    TitleBar: typeof import('./src/components/TitleBar.vue')['default']
    WaterBall: typeof import('./src/components/WaterBall.vue')['default']
  }
}
