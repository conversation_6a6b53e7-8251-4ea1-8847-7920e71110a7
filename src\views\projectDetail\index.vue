<template>
  <div class="relative w-full h-full origin-[0_0] overflow-hidden bg-[#0a0f1a] bg-[right_bottom] z-[2]">
    <div class="content relative">
      <!-- 标题部分保持不变 -->
      <div
        class="absolute w-1/2 text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title z-10 top-0 left-2/4 transform -translate-x-1/2">
        {{ title }}
      </div>
      <div
        class="absolute w-1/2 text-center text-[28px] font-family-youshebiaotihei tracking-[8px] title z-10 top-[120px] left-2/4 transform -translate-x-1/2">
        {{ projectData.项目名称 }}</div>
      <div class="bg"></div>
      <div class="leftBg"></div>
      <div class="bottomBg"></div>
      
      <!-- 左侧按钮 -->
      <div class="btns">
        <div v-for="(item, index) in filteredBtns" 
          :class="[
            btnsActive == index && 'active', 
            (isInternationalProject && index > 0 || isDesignProject && index > 0) && 'disabled'
          ]" 
          :key="index"
          @click="handleBtnClick(index)">
          <span> {{ item.name }}</span>
        </div>
      </div>
      
      <!-- 缩略图容器 - PC版本样式 -->
      <div v-if="!isUsingDefaultImage && mediaItems.length > 0" class="thumbnails-container">
        <div v-for="(mediaItem, index) in mediaItems" :key="index" class="thumbnail"
          :class="{ 'active': currentMediaIndex === index }">
          <!-- 图片缩略图 -->
          <img v-if="mediaItem.type === 'image'" :src="mediaItem.thumbnail" 
            :alt="t('projectDetail.actions.thumbnailAlt')"
            @click="() => handleThumbnailClick(index, mediaItem)" />
          
          <!-- 视频缩略图 -->
          <div v-else-if="mediaItem.type === 'video'" class="video-thumbnail-container"
            @click="() => handleThumbnailClick(index, mediaItem)">
            <video :src="mediaItem.thumbnail" 
              class="video-thumbnail" 
              muted 
              preload="metadata"
              @loadedmetadata="($event) => {
                $event.target.currentTime = 2;
              }">
            </video>
            <div class="video-play-icon">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M8,5.14V19.14L19,12.14L8,5.14Z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 背景图/视频展示 -->
      <div class="map" v-if="currentMediaType === 'image'" :style="backgroundStyle"></div>
      <div class="map video-container" v-else-if="currentMediaType === 'video' && currentVideoSrc">
        <video 
          :src="currentVideoSrc" 
          autoplay 
          loop 
          muted
          class="background-video">
        </video>
      </div>
      
      <!-- 内容区域容器 - 固定900px宽度 -->
      <div class="content-wrapper">
        <item1 :projectData="projectData" :isInternationalProject="isInternationalProject" :personData="personData" v-if="btnsActive == 0" />
        <item2 :projectData="projectData" v-if="btnsActive == 1" />
        <item3 :projectData="projectData" v-if="btnsActive == 2" />
      </div>
      
      <!-- 返回按钮 - PC版本样式 -->
      <div class="backHome-button" @click="backHome">
        <img src="@/assets/images/header/back.png" alt="" />
        <span>返回首页</span>
      </div>
      <div class="back-button" @click="back">
        <span>返回上一级</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useI18n } from 'vue-i18n';
import item1 from "./components/item1.vue";
import item2 from "./components/item2.vue";
import item3 from "./components/item3.vue";
import request from "@/utils/request";
import defaultMapImg from '@/assets/images/project/mapImg.png';

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

const title = ref("");
const projectName = localStorage.getItem("clickProject");
const projectData = ref({});
const personData = ref({});

const isChina = ref(localStorage.getItem("isChina") ? true : false);
const isInternationalProject = ref(false);

const isDesignProject = computed(() => {
  return projectData.value && projectData.value.项目类别 === '设计';
});

const btns = ref([
  {
    id: 1,
    name: t('projectDetail.tabs.overview'),
  },
  {
    id: 2,
    name: t('projectDetail.tabs.qualitySafety'),
  },
  {
    id: 3,
    name: t('projectDetail.tabs.equipmentFacilities'),
  },
]);

const filteredBtns = computed(() => {
  return btns.value.map(btn => ({
    ...btn,
    name: btn.id === 1 ? t('projectDetail.tabs.overview') :
      btn.id === 2 ? t('projectDetail.tabs.qualitySafety') :
        btn.id === 3 ? t('projectDetail.tabs.equipmentFacilities') :
          btn.name
  }));
});

const btnsActive = ref(0);
const btnsChange = (val) => {
  btnsActive.value = val;
  localStorage.setItem("btnsActive", val);
};

const handleBtnClick = (index) => {
  if ((isInternationalProject.value || isDesignProject.value) && index > 0) {
    return;
  }
  btnsChange(index);
};

const images = ref([]);
const videos = ref([]);
const mediaItems = ref([]);
const currentImageIndex = ref(0);
const currentMediaIndex = ref(0);
const currentMediaType = ref('image');
const isUsingDefaultImage = ref(true);

// 计算当前图片源
const currentImageSrc = computed(() => {
  if (currentMediaType.value === 'image' && images.value.length > 0) {
    return images.value[currentImageIndex.value];
  }
  return null;
});

// 计算当前视频源
const currentVideoSrc = computed(() => {
  if (currentMediaType.value === 'video' && videos.value.length > 0) {
    const videoIndex = currentMediaIndex.value - images.value.length;
    return videos.value[videoIndex];
  }
  return null;
});

// 背景样式计算
const backgroundStyle = computed(() => {
  const currentImg = currentImageSrc.value;
  
  if (!currentImg) {
    return {
      backgroundColor: "transparent",
    };
  }

  return {
    backgroundImage: `url(${currentImg})`,
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center",
    backgroundSize: "100% 100%",
  };
});

// 照片处理函数
async function getProjectPhotoUrls(photoIds) {
  if (!photoIds) {
    return [defaultMapImg];
  }

  try {
    const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");

    if (tokenRes.code !== 0 || !tokenRes.data) {
      console.error("获取token失败:", tokenRes);
      return [defaultMapImg];
    }

    const token = tokenRes.data;
    const userId = "941981453197164545";
    const photoIdArray = photoIds.split(',').filter(id => id.trim());

    if (photoIdArray.length === 0) {
      return [defaultMapImg];
    }

    const photoUrls = [];
    const maxPhotos = Math.min(photoIdArray.length, 5);

    for (let i = 0; i < maxPhotos; i++) {
      const photoId = photoIdArray[i].trim();
      if (photoId) {
        try {
          const photoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${photoId}?access_token=${token}&userid=${userId}`;
          const isValid = await validateImageUrl(photoUrl);
          if (isValid) {
            photoUrls.push(photoUrl);
          }
        } catch (err) {
          console.warn(`图片${photoId}处理失败:`, err);
          continue;
        }
      }
    }

    return photoUrls.length > 0 ? photoUrls : [defaultMapImg];
  } catch (err) {
    console.error("处理照片失败:", err);
    return [defaultMapImg];
  }
}

// 验证图片URL
function validateImageUrl(url) {
  return new Promise((resolve) => {
    const img = new Image();
    const timeout = setTimeout(() => {
      resolve(false);
    }, 5000);

    img.onload = () => {
      clearTimeout(timeout);
      resolve(true);
    };

    img.onerror = () => {
      clearTimeout(timeout);
      resolve(false);
    };

    img.src = url;
  });
}

// 视频处理函数
async function getProjectVideoUrls(videoIds) {
  if (!videoIds) {
    return [];
  }

  try {
    const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");

    if (tokenRes.code !== 0 || !tokenRes.data) {
      console.error("获取token失败:", tokenRes);
      return [];
    }

    const token = tokenRes.data;
    const userId = "941981453197164545";
    const videoIdArray = videoIds.split(',').filter(id => id.trim());

    if (videoIdArray.length === 0) {
      return [];
    }

    const maxVideos = Math.min(videoIdArray.length, 5);
    const videoUrls = videoIdArray.slice(0, maxVideos).map(videoId => {
      const trimmedId = videoId.trim();
      return `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${trimmedId}?access_token=${token}&userid=${userId}`;
    });

    return videoUrls;
  } catch (err) {
    console.error("处理视频失败:", err);
    return [];
  }
}

// 合并媒体项目
function mergeMediaItems() {
  const items = [];
  
  // 处理效果图
  if (projectData.value && projectData.value.项目效果图) {
    const effectImageIds = projectData.value.项目效果图.split(',').filter(id => id.trim());
    
    effectImageIds.forEach((effectImageId, index) => {
      const trimmedId = effectImageId.trim();
      const effectImageUrl = images.value.find(imgUrl => imgUrl.includes(trimmedId));
      
      if (effectImageUrl) {
        items.push({
          type: 'image',
          url: effectImageUrl,
          index: items.length,
          thumbnail: effectImageUrl,
          isEffectImage: true
        });
      }
    });
  }
  
  // 添加其他图片
  const effectImageUrls = items.filter(item => item.isEffectImage).map(item => item.url);
  
  images.value.forEach((image, idx) => {
    if (!effectImageUrls.includes(image)) {
      items.push({
        type: 'image',
        url: image,
        index: idx,
        thumbnail: image
      });
    }
  });
  
  // 添加视频
  videos.value.forEach((video) => {
    items.push({
      type: 'video',
      url: video,
      index: items.length,
      thumbnail: video
    });
  });
  
  mediaItems.value = items;
  
  // 初始化第一个媒体项目
  if (items.length > 0) {
    currentMediaIndex.value = 0;
    const firstItem = items[0];
    if (firstItem.type === 'image') {
      currentImageIndex.value = 0;
      currentMediaType.value = 'image';
    } else if (firstItem.type === 'video') {
      currentMediaType.value = 'video';
    }
  }
}

// 处理项目照片
async function processProjectPhotos(data) {
  let hasAnyMedia = false;
  
  // 处理照片
  if (data.项目照片) {
    try {
      const photoUrls = await getProjectPhotoUrls(data.项目照片);
      if (photoUrls.length > 0 && photoUrls[0] !== defaultMapImg) {
        images.value = photoUrls;
        hasAnyMedia = true;
      } else {
        images.value = [];
      }
    } catch (err) {
      console.error("处理项目图片失败:", err);
      images.value = [];
    }
  } else {
    images.value = [];
  }

  // 处理效果图
  if (data.项目效果图) {
    try {
      const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");
      if (tokenRes.code === 0 && tokenRes.data) {
        const token = tokenRes.data;
        const userId = "941981453197164545";
        const effectImageIds = data.项目效果图.split(',').filter(id => id.trim());
        
        for (let i = 0; i < effectImageIds.length; i++) {
          const effectImageId = effectImageIds[i].trim();
          if (effectImageId) {
            const effectImageUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${effectImageId}?access_token=${token}&userid=${userId}`;
            const isValid = await validateImageUrl(effectImageUrl);
            if (isValid) {
              const exists = images.value.some(img => img === effectImageUrl);
              if (!exists) {
                images.value.push(effectImageUrl);
                hasAnyMedia = true;
              }
            }
          }
        }
      }
    } catch (err) {
      console.error("处理项目效果图失败:", err);
    }
  }

  // 处理视频
  if (data.项目视频) {
    try {
      const videoUrls = await getProjectVideoUrls(data.项目视频);
      if (videoUrls.length > 0) {
        videos.value = videoUrls;
        hasAnyMedia = true;
      } else {
        videos.value = [];
      }
    } catch (err) {
      console.error("处理项目视频失败:", err);
      videos.value = [];
    }
  } else {
    videos.value = [];
  }

  // 设置默认图片
  if (!hasAnyMedia) {
    images.value = [defaultMapImg];
    isUsingDefaultImage.value = true;
  } else {
    isUsingDefaultImage.value = false;
  }

  mergeMediaItems();
}

// 获取项目数据
async function fetchProjectData() {
  try {
    let queryData = {}
    try {
      queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    } catch (e) {
      console.error("解析项目数据失败", e);
    }
    const projectId = queryData.code || "";
    const isGwParam = queryData.isGw || "false";
    const isGw = String(isGwParam).toLowerCase();

    isInternationalProject.value = isGw === "true";

    let res;
    if (isGw === "true") {
      res = await request.get("/globalManage/zjmanage/largescreen/getXmxxV2", {
        params: { id: projectId }
      });
    } else {
      res = await request.get("/globalManage/zjmanage/largescreen/getXmjbxx", {
        params: { id: projectId }
      });
    }

    if (res.code === 0 && res.data && res.data.length > 0) {
      const data = res.data[0];

      if (isGw === "true") {
        title.value = data.国家 || "";
        projectData.value = data;
        await processProjectPhotos(data);
      } else {
        title.value = data.省 || "";
        projectData.value = data;

        // 国内项目：尝试获取渲染文件
        if (data.code) {
          try {
            const renderingRes = await request.post("/globalManage/zjmanage/largescreen/getRenderingByCode", {
              projectCode: data.code
            });
            
            if (renderingRes.code === 0 && renderingRes.data && renderingRes.data.length > 0) {
              projectData.value.renderingFiles = renderingRes.data;
              const renderingImages = renderingRes.data
                .filter(file => file.downloadUrl)
                .map(file => file.downloadUrl);
              
              if (renderingImages.length > 0) {
                images.value = renderingImages;
                isUsingDefaultImage.value = false;
              } else {
                await processProjectPhotos(data);
              }
            } else {
              await processProjectPhotos(data);
            }
          } catch (renderingErr) {
            console.error("调用渲染接口失败:", renderingErr);
            await processProjectPhotos(data);
          }
        } else {
          await processProjectPhotos(data);
        }
      }
    } else {
      images.value = [defaultMapImg];
      isUsingDefaultImage.value = true;
    }
  } catch (err) {
    console.error("获取项目数据失败:", err);
    images.value = [defaultMapImg];
    isUsingDefaultImage.value = true;
  }
}

// 获取人员数据
async function getPersonData() {
  try {
    const queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    const projectCode = queryData.code || "";

    if (!projectCode) {
      console.warn("项目code为空，无法获取人员数据");
      return {};
    }

    const res = await request.post("/globalManage/deviceMonitor/getNumberPerson", {
      projectCode: projectCode
    });

    if (res.code === 0 && res.data) {
      return res.data;
    } else {
      console.warn("获取人员数据失败或无数据:", res);
      return {};
    }
  } catch (err) {
    console.error("获取人员数据失败:", err);
    return {};
  }
}

onMounted(() => {
  fetchProjectData().then(() => {
    const queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    const isGw = queryData.isGw === "true";
    
    if (!isGw) {
      getPersonData().then(data => {
        personData.value = data;
      });
    }
  });
});

watch([images, videos], () => {
  mergeMediaItems();
});

// PC版本的返回逻辑
const back = () => {
  localStorage.removeItem("clickProject");

  const transferData = {
    level: '',
    title: ''
  };

  const clickProject = sessionStorage.getItem("clickProject");
  if (clickProject) {
    try {
      const queryData = JSON.parse(clickProject || "{}");
      const isGw = queryData.isGw === "true";

      if (isGw) {
        transferData.level = 'country';
        transferData.title = projectData.value.国家 || title.value;
      } else {
        transferData.level = 'district';
        transferData.title = projectData.value.区 || '';
      }

      if (!transferData.title && !isGw) {
        transferData.level = 'city';
        transferData.title = projectData.value.市 || '';
      }

      if (!transferData.title && !isGw) {
        transferData.level = 'province';
        transferData.title = projectData.value.省 || '';
      }
    } catch (e) {
      console.error("解析项目数据失败", e);
    }
  }

  sessionStorage.setItem("countryLevel", transferData.level);
  sessionStorage.setItem("countryTitle", transferData.title);

  router.replace({
    path: "/project/country"
  });
};

// PC版本的返回首页逻辑
const backHome = () => {
  localStorage.removeItem("isChina");
  localStorage.removeItem("clickProject");
  sessionStorage.removeItem("countryLevel");
  sessionStorage.removeItem("countryTitle");
  sessionStorage.removeItem("clickCountry");
  sessionStorage.removeItem("lastProjectState");
  sessionStorage.removeItem("clickProject");
  
  router.push("/project/index");
  
  setTimeout(() => {
    const iframe = document.getElementById("iframe");
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage(
        { eve: "changeModel", data: "earth", model: "earth" },
        "*"
      );
    }
  }, 500);
};

// 处理缩略图点击
const handleThumbnailClick = (index, mediaItem) => {
  currentMediaIndex.value = index;
  
  if (mediaItem.type === 'image') {
    // 找到图片在images数组中的实际索引
    const imageIndex = images.value.findIndex(img => img === mediaItem.url);
    if (imageIndex !== -1) {
      currentImageIndex.value = imageIndex;
    }
    currentMediaType.value = 'image';
  } else if (mediaItem.type === 'video') {
    currentMediaType.value = 'video';
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  pointer-events: all;
  position: relative;
  z-index: 1;

  .bg {
    width: 100%;
    height: 150px;
    position: absolute;
    top: -28px;
    left: 0;
    background: url("@/assets/images/header/headerBgFont.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 3;
  }

  .leftBg {
    width: 370px;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAQ4CAYAAAD/4XnLAAAACXBIWXMAAAsSAAALEgHS3X78AAAJt0lEQVR") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 2;
    opacity: 0.8; // 降低不透明度，为渐变效果做准备
  }

  .bottomBg {
    width: 100%;
    height: 160px;
    position: absolute;
    left: 0;
    bottom: 0;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 2;
  }

  .btns {
    width: 36px;
    height: 500px;
    position: absolute;
    top: 115px;
    left: 5px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 6;

    >div {
      width: 36px;
      height: 160px;
      background: url("@/assets/images/project/btn.png") no-repeat;
      background-size: 100% 100%;
      color: #97acb4;
      box-sizing: border-box;
      padding: 0 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      cursor: pointer;

      &.active {
        background: url("@/assets/images/project/btnActive.png") no-repeat;
        background-size: 100% 100%;
        color: #fff;
      }
      
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        filter: grayscale(0.8);
        pointer-events: none;
      }
    }
  }

  .map {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    
    &.video-container {
      background: none;
      
      .background-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  // 内容容器 - 固定900px宽度
  .content-wrapper {
    height: 100%;
    width: 900px;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 5;
    
    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.back-button {
  position: absolute;
  top: 25px;
  left: 170px;
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  text-shadow: 0px 0px 4px #0085FF;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.backHome-button {
  position: absolute;
  top: 25px;
  left: 60px;
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  text-shadow: 0px 0px 4px #0085FF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  
  img {
    width: 18px;
    height: 18px;
  }
}

// PC版本缩略图容器样式
.thumbnails-container {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 8;
  pointer-events: auto;
  max-height: 80vh;
  overflow-y: auto;
  
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.thumbnail {
  width: 100px;
  height: 70px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  position: relative;
  
  &:hover {
    border-color: rgba(0, 89, 255, 0.6);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 89, 255, 0.3);
  }
  
  &.active {
    border-color: #0080ff;
    box-shadow: 0 0 15px rgba(0, 47, 255, 0.6);
    transform: scale(1.08);
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
    
    &:hover {
      opacity: 0.9;
    }
  }
  
  .video-thumbnail-container {
    position: relative;
    width: 100%;
    height: 100%;
    
    .video-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.3s ease;
      
      &:hover {
        opacity: 0.9;
      }
    }
    
    .video-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(0, 89, 255, 0.8);
        transform: translate(-50%, -50%) scale(1.1);
      }
      
      svg {
        margin-left: 2px;
      }
    }
  }
}
</style>