<template>
  <div class="project-detail-container">
    <!-- 国外项目展示 -->
    <template v-if="isInternationalProject">
      <!-- 工程概况 -->
      <div class="section engineering-overview">
        <div class="info-section">
          <div class="info-section-bg">工程概况</div>
        </div>
        <div class="overview-content">
          <div class="overview-row">
            <div class="overview-item">
              <span class="label">项目业主方：</span>
              <span class="value">{{ processedData.项目业主方 }}</span>
            </div>
          </div>
          <div class="overview-row">
            <div class="overview-item">
              <span class="label">资金来源：</span>
              <span class="value">{{ processedData.资金来源 }}</span>
            </div>
          </div>
          <div class="overview-row">
            <div class="overview-item">
              <span class="label">建设内容：</span>
              <el-tooltip
                effect="dark"
                :content="processedData.建设内容"
                placement="top-start"
                :show-after="200"
                :hide-after="100"
                :enterable="false"
                popper-class="custom-tooltip-popper wide-tooltip"
                :disabled="!processedData.建设内容 || processedData.建设内容.length <= 15"
              >
                <span class="value text-ellipsis-multiline clickable-text">{{ processedData.建设内容 }}</span>
              </el-tooltip>
            </div>
          </div>
          <div class="overview-row">
            <div class="overview-item">
              <span class="label">合同类型：</span>
              <span class="value">{{ processedData.合同类型 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目统计数据 -->
      <div class="section project-stats">
        <div class="stats-grid">
          <div class="stat-item">
            <RotatingCircle style="width: 60px;height: 60px;">
              <svg-icon name="dollar" color="#fff" style="width: 30px;height: 30px;" />
            </RotatingCircle>
            <div class="stat-content">
              <div class="stat-label">合同金额</div>
              <div class="stat-value">
                <span class="number">{{ formatContractAmount(processedData.合同额) }}</span>
                <span class="unit">万美元</span>
              </div>
            </div>
          </div>

          <div class="stat-item">
            <RotatingCircle style="width: 60px;height: 60px;">
              <svg-icon name="duration" color="#fff" style="width: 30px;height: 30px;" />
            </RotatingCircle>
            <div class="stat-content">
              <div class="stat-label">总工期</div>
              <div class="stat-value">
                <span class="number">{{ processedData.工期 }}</span>
                <span class="unit">天</span>
              </div>
            </div>
          </div>

          <div class="stat-item">
            <RotatingCircle style="width: 60px;height: 60px;">
              <svg-icon name="startWork" color="#fff" style="width: 30px;height: 30px;" />
            </RotatingCircle>
            <div class="stat-content">
              <div class="stat-label">开工日期</div>
              <div class="stat-value">
                <span class="number">{{ formatDate(processedData.开工日期) }}</span>
                <span class="unit"></span>
              </div>
            </div>
          </div>

          <div class="stat-item">
            <RotatingCircle style="width: 60px;height: 60px;">
              <svg-icon name="startWork" color="#fff" style="width: 30px;height: 30px;" />
            </RotatingCircle>
            <div class="stat-content">
              <div class="stat-label">计划竣工日期</div>
              <div class="stat-value">
                <span class="number">{{ formatDate(processedData.计划竣工日期) }}</span>
                <span class="unit"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目人员 -->
      <div class="section project-personnel">
        <div class="info-section">
          <div class="info-section-bg">项目人员</div>
        </div>
        <div class="personnel-content">
          <div class="personnel-stats">
            <div class="personnel-item">
              <RotatingCircle style="width: 60px;height: 60px;">
                <svg-icon name="user-3-fill" color="#fff" style="width: 30px;height: 30px;" />
              </RotatingCircle>
              <div class="personnel-label">项目总人员</div>
              <div class="personnel-value">
                <span class="number">{{ processedData.项目总人数 }}</span>
                <span class="unit">人</span>
              </div>
            </div>
            <!-- 添加饼图展示中方与外籍比例 -->
          <div class="chart">
            <InternationalPieChart :data="internationalPieData"></InternationalPieChart>
          </div>
          </div>
        </div>
      </div>

       <!-- 施工进度 -->
      <div class="section construction-progress">
        <div class="info-section">
          <div class="info-section-bg">施工进度</div>
        </div>
        <div class="progress-content">
          <div class="progress-stats">
            <div class="progress-item">
              <RotatingCircle style="width: 60px;height: 60px;">
                <svg-icon name="startWork" color="#fff" style="width: 30px;height: 30px;" />
              </RotatingCircle>
              <div class="progress-content-item">
                <div class="progress-label">施工进度</div>
                <div class="progress-value">
                  <span class="number">{{ formatProgress(processedData.施工进度) }}</span>
                  <span class="unit">%</span>
                </div>
              </div>
            </div>

            <div class="progress-item">
              <RotatingCircle style="width: 60px;height: 60px;">
                <svg-icon name="dollar" color="#fff" style="width: 30px;height: 30px;" />
              </RotatingCircle>
              <div class="progress-content-item">
                <div class="progress-label">累计完成产值</div>
                <div class="progress-value">
                  <span class="number">{{ formatContractAmount(processedData.累计完成产值) }}</span>
                  <span class="unit">万美元</span>
                </div>
              </div>
            </div>

            <div class="progress-item">
              <RotatingCircle style="width: 60px;height: 60px;">
                <svg-icon name="dollar" color="#fff" style="width: 30px;height: 30px;" />
              </RotatingCircle>
              <div class="progress-content-item">
                <div class="progress-label">回款总额</div>
                <div class="progress-value">
                  <span class="number">{{ formatContractAmount(processedData.回款总额 || processedData.工程回款总额) }}</span>
                  <span class="unit">万美元</span>
                </div>
              </div>
            </div>

            <div class="progress-item">
              <RotatingCircle style="width: 60px;height: 60px;">
                <svg-icon name="duration" color="#fff" style="width: 30px;height: 30px;" />
              </RotatingCircle>
              <div class="progress-content-item">
                <div class="progress-label">剩余工期</div>
                <div class="progress-value">
                  <span class="number">{{ processedData.剩余工期 }}</span>
                  <span class="unit">天</span>
                </div>
              </div>
            </div>
          </div>

          <div class="progress-description">
            <div class="overview-item">
              <span class="label">形象进度：</span>
              <el-tooltip
                effect="dark"
                :content="processedData.形象进度描述"
                placement="top-start"
                :show-after="200"
                :hide-after="100"
                :enterable="false"
                popper-class="custom-tooltip-popper wide-tooltip"
                :disabled="!processedData.形象进度描述 || processedData.形象进度描述.length <= 15"
              >
                <span class="value text-ellipsis-multiline clickable-text" style="white-space: pre-line">{{ processedData.形象进度描述 }}</span>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 国内项目展示（使用原来的模板样式） -->
    <template v-else>
      <div class="left-content">
        <div class="left-item one">
          <div class="top">
            <div class="project-title">
              {{ '项目基本信息' }}
            </div>
            <div class="block ml-[-20px]">
              <div class="grid grid-cols-2 gap-4">
                <div class="item" v-for="(item, index) in list1" :key="index">
                  <div class="icon">
                    <img :src="item.icon" alt="" />
                  </div>
                  <div class="info">
                    <div class="title font-light text-[14px]">{{ item.title }}</div>
                    <div class="num">
                      <span class="text-[20px] font-family-oswald-medium" :style="{ color: item.itemStyle.color }">
                        {{ index == 3 ? item.num : formatNumber(item.num) }}</span>
                      <span class="text-[#D1D3D5] text-[14px] font-light ml-[4px]">{{
                        item.unit
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="left-item two">
          <div class="top">
            <div class="project-title mt-[20px]">{{ t('projectDetail.overview.projectManagement') }}</div>
            <div class="block ml-[-20px]">
              <div class="grid grid-cols-2 gap-4">
                <div class="item" v-for="(item, index) in list2" :key="index">
                  <div class="icon">
                    <img :src="item.icon" alt="" />
                  </div>
                  <div class="info">
                    <div class="title font-light text-[14px]">{{ item.title }}</div>
                    <div class="num">
                      <span class="text-[24px] font-family-oswald-medium" :style="{ color: item.itemStyle.color }">
                        {{ formatNumber(item.num) }}</span>
                      <span class="text-[#D1D3D5] text-[14px] font-light ml-[10px]">{{
                        item.unit
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="three">
          <div class="three-one">
            <div class="project-title mt-[20px]">项目人员</div>
            <div class="block">
              <div class="item" v-for="(item, index) in list3" :key="index">
                <div class="icon">
                  <img :src="item.icon" alt="" />
                </div>
                <div class="title">{{ item.title }}</div>
                <div class="num">
                  <span class="text-[24px] font-family-oswald-medium" :style="{ color: item.itemStyle.color }">
                    {{ formatNumber(item.num) }}</span>
                  <span class="text-[#D1D3D5] text-[14px] ml-[10px]">{{
                    item.unit
                  }}</span>
                </div>
              </div>
            </div>
            <div class="chart">
              <PieChart :data="pieData"></PieChart>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { nextTick, ref, watch, onMounted, computed } from "vue";
import { useI18n } from 'vue-i18n';
import { getImg, formatNumber } from "@/utils/method";
import RotatingCircle from "@/components/RotatingCircle.vue";
import SvgIcon from '@/components/SvgIcon.vue';
import PieChart from "./PieChart.vue";
import InternationalPieChart from "./InternationalPieChart.vue";

const { t } = useI18n();

const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({})
  },
  isInternationalProject: {
    type: Boolean,
    default: false
  }
});

// 国内项目数据
const domesticTitle = ref(t('projectDetail.overview.projectTitle'));

// 项目基本信息数据
const list1 = ref([
  {
    id: 1,
    title: t('projectDetail.overview.basicInfo.contractAmount'),
    num: 0,
    unit: t('projectDetail.units.tenThousandYuan'),
    icon: getImg("project/icon1.png"),
    itemStyle: { color: "#a3d2eb" },
  },
  {
    id: 2,
    title: t('projectDetail.overview.basicInfo.projectOutput'),
    num: 0,
    unit: t('projectDetail.units.tenThousandYuan'),
    icon: getImg("project/icon1.png"),
    itemStyle: { color: "#a3d2eb" },
  },
  {
    id: 3,
    title: t('projectDetail.overview.basicInfo.totalDuration'),
    num: 0,
    unit: t('projectDetail.units.days'),
    icon: getImg("project/icon2.png"),
    itemStyle: { color: "#a3d2eb" },
  },
  {
    id: 4,
    title: t('projectDetail.overview.basicInfo.startDate'),
    num: "",
    icon: getImg("project/icon3.png"),
    unit: "",
    itemStyle: { color: "#a3d2eb" },
  },
]);

// 项目管理数据
const list2 = ref([
  {
    id: 1,
    title: t('projectDetail.overview.basicInfo.constructionProgress'),
    num: 45.37,
    unit: t('projectDetail.units.percent'),
    icon: getImg("project/icon4.png"),
    itemStyle: { color: "#a3d2eb" },
  },
  {
    id: 2,
    title: t('projectDetail.overview.basicInfo.engineeringPayment'),
    num: 1100,
    unit: t('projectDetail.units.tenThousandYuan'),
    icon: getImg("project/icon5.png"),
    itemStyle: { color: "#a3d2eb" },
  },
]);

// 人员数据
const list3 = ref([
  {
    id: 1,
    title: t('projectDetail.overview.basicInfo.totalPersonnel'),
    num: 211,
    unit: t('projectDetail.units.people'),
    icon: getImg("project/icon6.png"),
    itemStyle: { color: "#a3d2eb" },
  },
]);

const pieData = ref([]);

// 格式化合同金额
const formatContractAmount = (amount) => {
  if (!amount) return '2,690.8';
  if (typeof amount === 'string') {
    return amount.replace(/,/g, '');
  }
  return formatNumber(amount);
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  if (typeof date === 'string' && date.includes(' ')) {
    return date.split(' ')[0].replace(/-/g, '/');
  }
  return date;
};

// 格式化进度
const formatProgress = (progress) => {
  if (!progress) return '89.9';
  if (typeof progress === 'string') {
    return progress.replace('%', '');
  }
  return progress;
};

// 格式化国内项目数据
const formatDomesticProjectData = (data) => {
  if (data) {
    console.log('国内项目数据:', data);
    domesticTitle.value = data.项目名称 || t('projectDetail.overview.projectTitle');

    // 基本信息处理 - 国内项目使用API返回的字段名
    list1.value[0].num = data.合同金额?.replace(/,/g, "") || data.项目合同金额?.replace(/,/g, "") || 0;
    list1.value[1].num = data.产值?.replace(/,/g, "") || data.项目累计产值?.replace(/,/g, "") || 0;
    list2.value[1].num = data.工程回款总额?.replace(/,/g, "") || data.项目累收工程款?.replace(/,/g, "") || 0;
    list1.value[2].num = data.总工期 || 0;
    // 处理开工日期 - 移除时间部分
    if (data.开工日期) {
      list1.value[3].num = data.开工日期.split(' ')[0] || "";
    } else {
      list1.value[3].num = "";
    }

    // 施工进度 - 优先使用直接提供的进度值
    // if (data.施工进度) {
    //   // 有直接进度值时直接使用
    //   const progressText = data.施工进度.toString().replace('%', '');
    //   list2.value[0].num = parseFloat(progressText) || 0;
    // } else 
    if (data.产值 && data.合同金额) {
      // 否则基于产值与合同金额比例计算
      const 产值 = data.产值.replace(/,/g, "");
      const 合同金额 = data.合同金额.replace(/,/g, "");
      list2.value[0].num = ((产值 / 合同金额) * 100).toFixed(2);
    } else if (data.项目累计产值 && data.项目合同金额) {
      // 尝试使用扩展字段名
      const 产值 = data.项目累计产值.replace(/,/g, "");
      const 合同金额 = data.项目合同金额.replace(/,/g, "");
      list2.value[0].num = ((产值 / 合同金额) * 100).toFixed(2);
    } else {
      list2.value[0].num = 0;
    }

    // 项目总人数 - 优先使用总人数字段
    if (data.项目总人数) {
      list3.value[0].num = Number(data.项目总人数);
    } else {
      // 否则合计各类人员
      const totalStaff =
        Number(data.正式员工 || data.项目正式员工人数 || 0) +
        Number(data.其他形式用工 || data.其他用工 || data.其他用工形式人员 || 0);
      list3.value[0].num = totalStaff;
    }

    // 设置饼图数据 - 只显示正式员工和其他形式用工
    const pieDataArray = [];

    // 添加正式员工数据
    if (data.正式员工) {
      pieDataArray.push({
        name: t('projectDetail.fields.formalEmployees'),
        value: Number(data.正式员工) || 0,
        itemStyle: { color: "rgba(0, 216, 255, 1)" },
      });
    }

    // 添加其他形式用工数据
    if (data.其他形式用工) {
      pieDataArray.push({
        name: t('projectDetail.fields.otherEmploymentForms'),
        value: Number(data.其他形式用工) || 0,
        itemStyle: { color: "rgba(0, 255, 187, 1)" },
      });
    }

    // 如果没有有效数据，添加一个默认项避免图表为空
    if (pieDataArray.length === 0) {
      pieDataArray.push({
        name: t('projectDetail.messages.noData'),
        value: 1,
        itemStyle: { color: "rgba(128, 128, 128, 0.5)" },
      });
    }

    pieData.value = pieDataArray;
  }
};

// 新增：处理接口数据的函数（用于国外项目）
const processProjectData = (data) => {
  if (!data) return {};
  console.log(data);
  // 根据接口数据示例进行映射
  return {
    项目名称: data.项目名称 || data.项目简称 || '',
    项目业主方: data.项目业主方 || '',
    资金来源: data.资金来源 || '',
    建设内容: data.建设内容 || '',
    合同类型: data.合同类型 || '',
    合同额: data.合同额 || data.合同金额 || '0',
    工期: data.工期 || data.总工期 || '0',
    开工日期: data.开工日期 || '',
    预付款: data.预付款 || '0',
    合同签订日期: data.合同签订日期 || '',
    计划竣工日期: data.计划竣工日期 || '',
    施工进度: data.施工进度 || '0',
    累计完成产值: data.累计完成产值 || data.产值 || '0',
    回款总额: data.回款总额 || data.工程回款总额 || data.汇款总额 || '0',
    剩余工期: data.剩余工期 || '0',
    形象进度描述: data.形象进度描述 || '',
    项目总人数: data.项目总人数 || '0',
    中方: data.中方 || '0',
    外籍: data.外籍 || '0'
  };
};

// 计算属性：处理后的项目数据
const processedData = computed(() => {
  return processProjectData(props.projectData);
});

// 国际项目人员饼图数据
const internationalPieData = computed(() => {
  if (!processedData.value) return [];
  
  const pieDataArray = [];
  
  // 添加中方人员数据
  if (processedData.value.中方) {
    pieDataArray.push({
      name: '中方人员',
      value: Number(processedData.value.中方) || 0,
      itemStyle: { color: "rgba(0, 216, 255, 1)" },
    });
  }
  
  // 添加外籍人员数据
  if (processedData.value.外籍) {
    pieDataArray.push({
      name: '外籍人员',
      value: Number(processedData.value.外籍) || 0,
      itemStyle: { color: "rgba(0, 255, 187, 1)" },
    });
  }
  
  // 如果没有有效数据，添加一个默认项避免图表为空
  if (pieDataArray.length === 0) {
    pieDataArray.push({
      name: '暂无数据',
      value: 1,
      itemStyle: { color: "rgba(128, 128, 128, 0.5)" },
    });
  }
  
  return pieDataArray;
});

onMounted(() => {
  console.log('项目数据:', props.projectData);
  console.log('是否为国际项目:', props.isInternationalProject);
  
  // 根据项目类型处理数据
  if (props.isInternationalProject) {
    console.log('处理国际项目数据:', processedData.value);
  } else {
    formatDomesticProjectData(props.projectData);
  }
});

watch(() => props.projectData, (newData) => {
  console.log('项目数据更新:', newData);
  
  // 根据项目类型处理数据
  if (props.isInternationalProject) {
    console.log('处理后的国际项目数据:', processProjectData(newData));
  } else {
    formatDomesticProjectData(newData);
  }
}, { deep: true });

watch(() => props.isInternationalProject, (newValue) => {
  console.log('项目类型变更:', newValue ? '国际项目' : '国内项目');
  
  // 项目类型变更时重新处理数据
  if (newValue) {
    console.log('切换到国际项目模式');
  } else {
    console.log('切换到国内项目模式');
    formatDomesticProjectData(props.projectData);
  }
});
</script>
<style>
.el-popper {
  max-width: 500px;
}
</style>
<style lang="scss" scoped>
.project-detail-container {
  width: 1000px;
  height: 100%;
  padding: 120px 20px 80px 50px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(to right, #0D121C 0%, #0f141f 70%, #18202e 80%, transparent 100%);
  z-index: 0;
  // overflow-y: auto;

  .section {
    // background: rgba(5, 15, 35, 0.8);
    // border: 1px solid rgba(77, 151, 255, 0.3);
    // border-radius: 8px;
    // padding: 20px;
    // margin-bottom: 16px;
  }

  /* 信息部分标题样式 - 参考item2.vue */
  .info-section {
    width: 400px;
    height: 35px;
    margin: 0 0 20px 0;

    .info-section-bg {
      width: 400px;
      height: 40px;
      background: url("@/assets/images/map/projectInfo.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      padding-left: 40px;
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-size: 20px;
      color: #FFFFFF;
      line-height: 23px;
      letter-spacing: 2px;
      text-align: justified;
      font-style: normal;
      text-transform: none;
    }
  }

  // 工程概况样式
  .engineering-overview {
    .overview-content {
      background: rgba(61,126,240,0.1);
      padding: 10px 20px 4px 20px;
      .overview-row {
        margin-bottom: 12px;

        .overview-item {
          display: flex;
          align-items: flex-start;

          &.full-width {
            flex-direction: column;
            
            .label {
              margin-bottom: 8px;
            }
          }

          .label {
            font-size: 16px;
            color: #A3D2EB;
            min-width: 120px;
            white-space: nowrap;
          }

          .value {
            font-size: 16px;
            color: #FFFFFF;
            line-height: 1.5;
            flex: 1;

                          &.text-ellipsis-multiline {
                display: -webkit-box;
                -webkit-line-clamp: 7;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                cursor: help;
                
                &:hover {
                  position: relative;
                  z-index: 1000;
                }
            }
          }
        }
      }
    }

    // 国内项目简化样式
    &.domestic {
      .overview-content.domestic {
        background: linear-gradient(135deg, rgba(61,126,240,0.15) 0%, rgba(30,100,220,0.1) 100%);
        border: 1px solid rgba(77, 151, 255, 0.2);
        border-radius: 8px;
        padding: 20px;
        
        .overview-row {
          margin-bottom: 16px;
          
          &:last-child {
            margin-bottom: 0;
          }

          .overview-item {
            .label {
              font-size: 18px;
              font-weight: 500;
              min-width: 100px;
              background: linear-gradient(90deg, #A3D2EB 0%, #7FB8E5 100%);
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            .value {
              font-size: 18px;
              font-weight: 600;
              color: #FFFFFF;
              text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
            }
          }
        }
      }
    }
  }

  // 项目统计数据样式
  .project-stats {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-content {
          flex: 1;
          display: flex;
          flex-direction: column;

          .stat-label {
            font-size: 16px;
            color: #A3D2EB;
            margin-bottom: 8px;
            line-height: 1.2;
          }

          .stat-value {
            display: flex;
            align-items: baseline;
            line-height: 1;

            .number {
              font-size: 24px;
              font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: bold;
}

            .unit {
              font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
              font-weight: normal;
              font-size: 14px;
              color: rgba(219, 233, 255, 0.8);
              line-height: 20px;
              text-align: right;
              font-style: normal;
              text-transform: none;
            }
          }
        }
      }
    }
  }

  // 施工进度样式
  .construction-progress {
    .progress-content {
              .progress-stats {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin-bottom: 20px;

        .progress-item {
          display: flex;
          align-items: center;
          gap: 16px;

          .progress-content-item {
            flex: 1;
            display: flex;
            flex-direction: column;

            .progress-label {
              font-size: 16px;
              color: #A3D2EB;
              margin-bottom: 8px;
              line-height: 1.2;
            }

            .progress-value {
              display: flex;
              align-items: baseline;
              line-height: 1;

              .number {
                font-size: 24px;
              font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: bold;
              }

              .unit {
                font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
                font-weight: normal;
                font-size: 14px;
                color: rgba(219, 233, 255, 0.8);
                line-height: 20px;
                text-align: right;
                font-style: normal;
                text-transform: none;
              }
            }
          }
        }
      }

        .progress-description {
          background: rgba(61,126,240,0.10);
          padding: 10px 20px 10px 20px;

          .overview-item {
            display: flex;
            align-items: flex-start;

            .label {
              font-size: 16px;
              color: #A3D2EB;
              min-width: 120px;
              white-space: nowrap;
            }

            .value {
              font-size: 16px;
              color: #FFFFFF;
              line-height: 1.5;
              flex: 1;
              white-space: pre-wrap;
              word-break: break-word;
              overflow-wrap: break-word;

              &.text-ellipsis-multiline {
                display: -webkit-box;
                -webkit-line-clamp: 10;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                cursor: help;
                
                &:hover {
                  position: relative;
                  z-index: 1000;
                }
              }
            }
          }
        }
    }
  }

  // 项目人员样式
  .project-personnel {
    .personnel-content {
      .personnel-stats {
        display: flex;
        align-items: center;
        gap: 40px;

        .personnel-item {
          display: flex;
          align-items: center;
          gap: 16px;

          .personnel-label {
            font-size: 16px;
            color: #A3D2EB;
          }

          .personnel-value {
            display: flex;
            align-items: baseline;

            .number {
              font-size: 24px;
              font-family: TCloudNumber;
              background-image: linear-gradient(to bottom,
                  #9AC4FF 0%,
                  #FFFFFF 50%,
                  #9AC4FF 100%);
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
              color: transparent;
              margin-right: 4px;
            }

            .unit {
              font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
              font-weight: normal;
              font-size: 14px;
              color: rgba(219, 233, 255, 0.8);
              line-height: 20px;
              text-align: right;
              font-style: normal;
              text-transform: none;
            }
          }
        }

        /* 饼图样式 */
        .chart {
          width: 360px;
          height: 160px;
          flex-shrink: 0;
        }
      }

      .personnel-breakdown {
        .breakdown-row {
          display: flex;
          gap: 40px;

          .breakdown-item {
            display: flex;
            align-items: center;

            .breakdown-label {
              font-size: 16px;
              color: #A3D2EB;
              margin-right: 16px;
              padding: 0 10px;
              background: linear-gradient( 180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%);
            }

            .breakdown-value {
              display: flex;
              align-items: baseline;

              .number {
                font-size: 20px;
                font-family: TCloudNumber;
                background-image: linear-gradient(to bottom,
                    #9AC4FF 0%,
                    #FFFFFF 50%,
                    #9AC4FF 100%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                color: transparent;
                margin-right: 4px;
              }

              .unit {
                font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
                font-weight: normal;
                font-size: 12px;
                color: rgba(219, 233, 255, 0.8);
                line-height: 20px;
                text-align: right;
                font-style: normal;
                text-transform: none;
              }
            }
          }
        }
      }
    }
  }

  /* 国内项目样式 */
  .left-content {
    width: 700px;
    height: 100%;
    padding: 120px 20px 220px 50px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 0;

    .left-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      z-index: 3;
    }

    .one {
      .block {
        display: flex;
        flex-direction: column;
        width: 80%;

        .grid {
          width: 100%;
        }

        .item {
          display: flex;
          margin-bottom: 20px;

          .num {
            white-space: nowrap;
          }

          .icon {
            width: 113px;
            height: 100px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .info {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            box-sizing: border-box;
            padding: 0 0 10px 10px;
          }
        }
      }
    }

    .two {
      .block {
        display: flex;
        flex-direction: column;
        width: 80%;

        .grid {
          width: 100%;
        }

        .item {
          display: flex;

          .icon {
            width: 113px;
            height: 100px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .info {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            box-sizing: border-box;
            padding: 0 0 10px 10px;
          }
        }
      }
    }

    .three {
      width: 100%;
      display: flex;
      justify-content: space-between;
      position: relative;
      z-index: 3;

      .three-one {
        width: 60%;
        display: flex;
        flex-direction: column;

        .block {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .item {
            width: 100%;
            display: flex;
            align-items: flex-end;

            .icon {
              width: 113px;
              height: 100px;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .title,
            .num {
              white-space: nowrap;
              margin-left: 20px;
              padding-bottom: 20px;
            }
          }
        }

        .chart {
          width: 100%;
          height: 250px;
        }
      }
    }

    /* 添加项目标题样式 */
    .project-title {
      width: 400px;
      height: 60px;
      background: url("@/assets/images/map/title.png") no-repeat;
      background-size: 100% 100%;
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-weight: 400;
      font-size: 20px;
      color: #F2F3FF;
      line-height: 24px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 10px #0094FF;
      text-align: left;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
      padding-left: 30px;
    }
  }

  /* 可点击文本样式 */
  .clickable-text {
    pointer-events: auto !important;
    cursor: help;
    position: relative;
    z-index: 10;
    
    &:hover {
      color: #A3D2EB !important;
      transition: color 0.2s ease;
    }
  }

  /* Element UI Tooltip 自定义样式 */
  :deep(.custom-tooltip-popper) {
    max-width: 450px !important;
    min-width: 280px !important;
    max-height: 280px !important;
    padding: 16px 20px !important;
    background: linear-gradient(135deg, rgba(5, 15, 35, 0.96) 0%, rgba(15, 25, 45, 0.96) 100%) !important;
    border: 1px solid rgba(77, 151, 255, 0.5) !important;
    border-radius: 10px !important;
    color: #FFFFFF !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    white-space: pre-wrap !important;
    overflow-y: auto !important;
    box-shadow: 
      0 10px 40px rgba(0, 0, 0, 0.5),
      0 0 30px rgba(77, 151, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(12px) !important;
    z-index: 10000 !important;

    /* 长文本内容的特殊宽度 */
    &.wide-tooltip {
      width: 500px !important;
      max-width: 650px !important;
      min-width: 400px !important;
    }
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 5px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.08);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(77, 151, 255, 0.6);
      border-radius: 3px;
      
      &:hover {
        background: rgba(77, 151, 255, 0.8);
      }
    }
  }

  /* Element UI Tooltip 箭头样式 */
  :deep(.custom-tooltip-popper[data-popper-placement^="top"] .el-popper__arrow::before) {
    background: linear-gradient(135deg, rgba(5, 15, 35, 0.96) 0%, rgba(15, 25, 45, 0.96) 100%) !important;
    border: 1px solid rgba(77, 151, 255, 0.5) !important;
  }
  
  :deep(.custom-tooltip-popper .el-popper__arrow) {
    &::before {
      background: linear-gradient(135deg, rgba(5, 15, 35, 0.96) 0%, rgba(15, 25, 45, 0.96) 100%) !important;
      border: 1px solid rgba(77, 151, 255, 0.5) !important;
    }
  }
}
</style>
