<template>
  <div class="auto-login-container">
    <div class="loading">
      <div class="spinner"></div>
      <p>正在自动登录中，请稍候...</p>
    </div>
  </div>
</template>

<script setup>
// 这个组件仅作为自动登录过程中的占位符
// 实际的登录逻辑在 permission.js 的路由守卫中处理
</script>

<style scoped>
.auto-login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading {
  text-align: center;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
