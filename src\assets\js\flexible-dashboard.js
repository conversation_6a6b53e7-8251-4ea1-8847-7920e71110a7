export const refreshScale = () => {
  let baseWidth = document.documentElement.clientWidth;
  let baseHeight = document.documentElement.clientHeight;
  let appStyle = document.getElementById("app").style;
  // let realRatio = baseWidth / baseHeight
  // let designRatio = 16 / 9
  let scaleRate = baseHeight / 1080;
  appStyle.transformOrigin = "center center";
  appStyle.transform = `translate(-50%, -50%) scale(${scaleRate})`;
  appStyle.width = `${baseWidth / scaleRate}px`;
};