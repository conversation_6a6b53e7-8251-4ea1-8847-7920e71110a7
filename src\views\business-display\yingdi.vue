<template>
  <div class="yingdi-container">
    <div class="content relative">
      <div
        class="absolute w-1/2 text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title z-10 top-0 left-2/4 transform -translate-x-1/2" style="z-index: 31; text-shadow: 0 2px 4px rgba(0,0,0,0.7);">
        {{ title }}
      </div>
      <div class="bg"></div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧信息区 -->
        <div class="left-content-container">
          <div class="info-section">
            <div class="info-section-bg">营地概览</div>
          </div>
          <div class="container">
            <!-- 营地信息列表 -->
            <div class="section project-info-list">
              <!-- 公司名 -->
              <div class="info-item">
                <div class="icon-container">
                  <img :src="getImg('project/icon1.png')" alt="公司图标" />
                </div>
                <div class="info-text">{{ companyName }}</div>
              </div>
              
              <!-- 所属大洲 -->
              <div class="info-item">
                <div class="icon-container">
                  <img :src="getImg('project/icon2.png')" alt="大洲图标" />
                </div>
                <div class="info-text">{{ continent }}</div>
              </div>
              
              <!-- 营地介绍 -->
              <div class="info-item description-item">
                <div class="icon-container">
                  <img :src="getImg('project/icon3.png')" alt="介绍图标" />
                </div>
                <div class="description-label">营地介绍：</div>
              </div>
              
              <!-- 介绍内容 -->
              <div class="description-content">
                <el-scrollbar height="700px">
                  <div class="text-content">{{ text }}</div>
                </el-scrollbar>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右边图片展示区域 -->
        <div class="right-image-section">
          <!-- 右边主要图片展示区域 -->
          <div class="right-image-container">
            <div class="image-display-area">
              <!-- 图片展示 -->
              <img v-if="previewType === 'image' && previewUrl" :src="previewUrl" alt="营地照片" />
              
              <!-- 视频展示 -->
              <video v-else-if="previewType === 'video' && previewUrl" :src="previewUrl" controls autoplay 
                style="width: 100%; height: 100%; object-fit: contain;"></video>
              
              <!-- 无内容展示 -->
              <div v-else class="no-image-placeholder">
                <span>暂无内容</span>
              </div>
            </div>
          </div>
          
          <!-- 缩略图容器 -->
          <div class="thumbnails-container" v-if="images.length > 0 || videos.length > 0">
            <!-- 图片缩略图 -->
            <template v-if="images.length > 0">
              <div v-for="(image, index) in images" :key="'image-' + index"
                :class="['thumbnail', previewUrl === image && 'active']"
                @click="previewMedia(image, 'image')">
                <img :src="image" alt="营地照片缩略图" />
              </div>
            </template>
            
            <!-- 视频缩略图 -->
            <template v-if="videos.length > 0">
              <div v-for="(video, index) in videos" :key="'video-' + index"
                :class="['thumbnail', previewUrl === video && 'active']"
                @click="previewMedia(video, 'video')">
                <div class="video-thumbnail-container">
                  <video :src="video" class="video-thumbnail" muted preload="metadata"
                    @loadedmetadata="($event) => { $event.target.currentTime = 2; }">
                  </video>
                  <div class="video-play-icon">
                    <svg viewBox="0 0 24 24" width="24" height="24">
                      <path fill="currentColor" d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                    </svg>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 返回首页按钮 -->
      <div class="backHome-button" @click="backHome">
        <img src="@/assets/images/header/back.png" alt="返回首页" />
        <span>返回首页</span>
      </div>
      <!-- 返回上一级按钮 -->
      <div class="back-button" @click="back">
        <span>返回上一级</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getImg } from "@/utils/method";
import request from "@/utils/request";
const router = useRouter();
const route = useRoute();

// 新增公司名和所属大洲的响应式变量
const companyName = ref(""); 
const continent = ref("");
const text = ref("");

// 新增预览相关状态
const previewUrl = ref("");
const previewType = ref("image");

// 预览媒体
const previewMedia = (url, type) => {
  previewUrl.value = url;
  previewType.value = type;
};

/**
 * 返回首页 - 完全重置到业务展示页面的初始状态
 * 这是用户希望的"首页"状态：地球模式 + 左右数据展示
 */
const backHome = () => {
  console.log('营地详情页面返回首页 - 完全重置状态');
  
  // 清除所有相关状态数据，确保返回到最初的页面状态
  localStorage.removeItem("isChina");
  localStorage.removeItem("clickProject");
  localStorage.removeItem("btnsActive");
  sessionStorage.removeItem("countryLevel");
  sessionStorage.removeItem("countryTitle");
  sessionStorage.removeItem("clickCountry");
  sessionStorage.removeItem("lastProjectState");
  sessionStorage.removeItem("clickProject");
  sessionStorage.removeItem("shouldResetToInitial");
  
  // 设置完全重置标记，确保业务展示页面恢复到初始状态
  sessionStorage.setItem("shouldResetToInitial", "true");
  
  // 跳转到业务布局首页
  router.replace({ path: "/business-display" });
};

/**
 * 返回上一级 - 返回到国家/地区详情页面
 */
const back = () => {
  console.log('营地详情页面返回上一级');
  
  // 构建要传递的数据
  const transferData = {
    level: '',
    title: ''
  };

  // 判断是国内还是国外项目
  const clickProject = sessionStorage.getItem("clickProject");
  if (clickProject) {
    try {
      const queryData = JSON.parse(clickProject || "{}");
      const isGw = queryData.isGw === "true";

      if (isGw) {
        // 国际项目：传递level和国家
        transferData.level = 'country';
        transferData.title = queryData.name || '';
      } else {
        // 中国项目：传递level和区域
        transferData.level = 'district';
        transferData.title = queryData.name || '';
      }
    } catch (e) {
      console.error("解析营地数据失败", e);
    }
  }

  // 设置返回到地区详情的状态
  sessionStorage.setItem("countryLevel", transferData.level);
  sessionStorage.setItem("clickCountry", transferData.title);
  localStorage.setItem("isChina", transferData.level === "country" ? "0" : "1");
  
  // 清理项目相关状态
  localStorage.removeItem("clickProject");
  sessionStorage.removeItem("clickProject");
  
  // 返回业务展示首页
  router.replace({ path: '/business-display' });
};
const title = ref("");
const longTitle = ref("");
const images = ref([]);
const videos = ref([]);

function generateImageUrls(pathStr, options = {}) {
  // 参数校验
  if (typeof pathStr !== "string") {
    throw new TypeError("路径参数必须是字符串类型");
  }

  // 合并配置选项
  const {
    separator = ";",
    prefix = "https://vr.ztmapinfo.com/yydpdatamedia.php?path=",
    allowedExtensions = [".jpg", ".jpeg", ".png", ".webp"],
  } = options;

  // 处理路径
  return pathStr
    .split(separator)
    .map((path) => path.trim())
    .filter((path) => {
      // 空路径过滤
      if (!path) return false;

      // 扩展名校验
      const extension = path.slice(path.lastIndexOf(".")).toLowerCase();
      return allowedExtensions.includes(extension);
    })
    .map((path) => {
      // 编码处理
      const encodedPath = encodeURIComponent(path);
      return `${prefix}${encodedPath}`;
    });
}
function generateVideoUrls(pathStr, options = {}) {
  // 参数校验
  if (typeof pathStr !== "string") {
    throw new TypeError("路径参数必须是字符串类型");
  }

  // 合并配置选项
  const {
    separator = ";",
    prefix = "https://vr.ztmapinfo.com/yydpdatamedia.php?path=",
    allowedExtensions = [".mp4", ".mov", ".avi", ".mkv"],
  } = options;

  // 路径处理流程
  return pathStr
    .split(separator) // 分割原始字符串
    .map((path) => path.trim()) // 去除两端空格
    .filter((path) => {
      // 过滤非法路径
      if (!path) return false;

      // 获取小写扩展名并验证
      const lastDotIndex = path.lastIndexOf(".");
      if (lastDotIndex === -1) return false;

      const extension = path.slice(lastDotIndex).toLowerCase();
      return allowedExtensions.includes(extension);
    })
    .map((path) => {
      // 生成编码后的URL
      const encodedPath = encodeURIComponent(path);
      return `${prefix}${encodedPath}`;
    });
}

// 获取营地照片
async function getYdPhotos(photoIds) {
  if (!photoIds) return;

  try {
    // 先获取token
    const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");

    if (tokenRes.code !== 0 || !tokenRes.data) {
      console.error("获取token失败:", tokenRes);
      return;
    }

    const token = tokenRes.data;
    const userId = "941981453197164545"; // 固定的userId

    // 处理照片ID列表
    const photoIdArray = photoIds.split(',').filter(id => id.trim());
    const photoUrls = [];

    // 处理所有图片，不再限制数量
    for (let i = 0; i < photoIdArray.length; i++) {
      const photoId = photoIdArray[i].trim();
      if (photoId) {
        // 构建照片URL
        photoUrls.push(`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${photoId}?access_token=${token}&userid=${userId}`);
      }
    }

    if (photoUrls.length > 0) {
      images.value = photoUrls;
      // 设置第一张图片为默认预览图
      if (!previewUrl.value) {
        previewMedia(photoUrls[0], 'image');
      }
    }
  } catch (err) {
    console.error("处理照片失败:", err);
  }
}

// 获取营地视频
async function getYdVideos(videoIds) {
  if (!videoIds) return;

  try {
    // 先获取token
    const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");

    if (tokenRes.code !== 0 || !tokenRes.data) {
      console.error("获取token失败:", tokenRes);
      return;
    }

    const token = tokenRes.data;
    const userId = "941981453197164545"; // 固定的userId

    // 处理视频ID列表
    const videoIdArray = videoIds.split(',').filter(id => id.trim());
    const videoUrls = [];

    for (let i = 0; i < videoIdArray.length; i++) {
      const videoId = videoIdArray[i].trim();
      if (videoId) {
        // 构建视频URL
        videoUrls.push(`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${videoId}?access_token=${token}&userid=${userId}`);
      }
    }

    if (videoUrls.length > 0) {
      videos.value = videoUrls;
      // 如果没有图片，则默认预览第一个视频
      if (!previewUrl.value && images.value.length === 0) {
        previewMedia(videoUrls[0], 'video');
      }
    }
  } catch (err) {
    console.error("处理视频失败:", err);
  }
}

// 获取营地数据
async function fetchYdData() {
  try {
    // 从sessionStorage获取数据而不是URL参数
    let queryData = {};
    try {
      queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    } catch (e) {
      console.error("解析营地数据失败", e);
    }

    // 获取营地code
    const ydId = queryData.code || "";
    if (!ydId) {
      console.error("没有找到营地ID");
      return;
    }

    // 请求营地信息
    const res = await request.get("/globalManage/zjmanage/largescreen/getYdxxV2", {
      params: { id: ydId }
    });

    if (res.code === 0 && res.data && res.data.length > 0) {
      const data = res.data[0];

      // 更新营地信息
      title.value = data.营地名称 || "";
      longTitle.value = data.营地名称 || "";
      text.value = data.营地介绍 || "";
      
      // 更新公司名和所属大洲
      companyName.value = data.公司名 || data.营地名称 || "未知";
      continent.value = data.所属大洲 || (data.所属国家 ? data.所属国家 + " 地区" : "未知");

      // 获取照片
      if (data.营地照片) {
        await getYdPhotos(data.营地照片);
      }

      // 获取视频
      if (data.营地视频) {
        await getYdVideos(data.营地视频);
      }
    } else {
      console.error("获取营地数据失败:", res);
    }
  } catch (err) {
    console.error("请求营地信息失败:", err);
  }
}

onMounted(() => {
  // 获取营地数据
  fetchYdData();
});
</script>

<style lang="scss" scoped>
.yingdi-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #0a0f1a;
  z-index: 2;
}
.info-section {
    width: 624px;
    height: 54.6px;
    margin: 80px 0 0px 0;  /* 将31.2px改为15px，减少下方间距 */

    .info-section-bg {
      width: 624px;
      height: 62.4px;
      background: url("@/assets/images/map/projectInfo.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      padding-left: 62.4px;
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-size: 31.2px;
      color: #FFFFFF;
      line-height: 35.88px;
      letter-spacing: 3.12px;
      text-align: justified;
      font-style: normal;
      text-transform: none;
    }
  }
.content {
  width: 100%;
  height: 100%;
  pointer-events: all;
  position: relative;
  z-index: 1000;

  .bg {
    width: 100%;
    height: 150px;
    position: absolute;
    top: -28px;
    left: 0;
    background: url("@/assets/images/header/headerBgFont.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 30;
  }

  .topBg {
    width: 100%;
    height: 240px;
    position: absolute;
    top: 0;
    right: 0;
    background: url("@/assets/images/yingdi/top.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 2;
  }

  // 主要内容区域
  .main-content {
    position: absolute;
    top: 70px;
    left: 50px;
    right: 20px;
    bottom: 50px;
    display: flex;
    gap: 20px;
    z-index: 1;
  }

  // 左边内容展示区域 (4/10)
  .left-content-container {
    flex: 1;
    min-width: 420px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0px; /* 将gap从20px改为0px */
    overflow-y: auto;
    padding-right: 10px;
    position: relative;
    
    // 隐藏滚动条
    -ms-overflow-style: none;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
    
    .container {
      width: 100%;
      height: calc(100% - 100px);
      display: flex;
      flex-direction: column;
      position: relative;
      z-index: 3;
      margin-top: 0px; /* 完全移除顶部间距 */
      
      // 项目信息列表样式
      .project-info-list {
        // background: rgba(1, 19, 67, 0.5);
        // border: 1px solid rgba(0, 133, 255, 0.3);
        // border-radius: 10px;
        padding: 20px;
        height: auto;
        margin-top: 0px; /* 移除额外的顶部间距 */
        
        // 信息项样式
        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 25px;
          padding: 15px 0;
          // border-bottom: 1px solid rgba(0, 133, 255, 0.2);
          
          &:last-child {
            border-bottom: none;
          }
          
          // 图标容器
          .icon-container {
            width: 160px;
            height: 150px;
            margin-right: 20px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
          
          // 信息文字
          .info-text {
            color: #FFFFFF;
            font-size: 46px;
            font-weight: bold;
            margin-top: 40px;
          }
          
          // 描述标签
          &.description-item {
            margin-bottom: 0;
            border-bottom: none;
            
            .description-label {
              color: #FFFFFF;
              font-size: 46px;
              font-weight: bold;
              margin-top: 40px;
            }
          }
        }
        
        // 描述内容
        .description-content {
          margin-left: 180px;
          margin-top: 10px;
          margin-bottom: 15px;
          
          .text-content {
            color: rgba(255, 255, 255, 0.8);
            font-size: 35px;
            line-height: 1.8;
            text-align: justify;
            white-space: pre-wrap;
          }
        }
      }
    }
  }

  // 右边图片展示区域 (6/10)
  .right-image-section {
    flex: 6;
    display: flex;
    height: 100%;
    position: relative;
    gap: 20px;
    align-items: flex-start;
    padding-top: 80px;
  }

  // 右边主要图片展示区域
  .right-image-container {
    flex: 1;
    height: calc(100% - 80px);
    
    .image-display-area {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      overflow: hidden;
      background: rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(5px);
      display: flex;
      align-items: center;
      justify-content: center;
      
      img, video {
        width: 100%;
        height: 100%;
        object-fit: contain;
        transition: all 0.3s ease;
      }
      
      .no-image-placeholder {
        color: rgba(255, 255, 255, 0.5);
        font-size: 18px;
        text-align: center;
      }
    }
  }

  // 缩略图容器
  .thumbnails-container {
    width: 280px;
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;
    gap: 16px;
    z-index: 8;
    pointer-events: auto;
    overflow-y: auto;
    flex-shrink: 0;
    padding-right: 12px;
    padding-top: 10px;
    margin: 0 20px;
    
    // 隐藏滚动条但保留滚动功能
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .thumbnails-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 28px;
      margin-bottom: 10px;
      padding-left: 10px;
      font-weight: bold;
      text-shadow: 0 1px 2px rgba(0, 133, 255, 0.5);
    }
  }
}

.back-button {
  position: absolute;
  top: 25px;
  left: 170px;
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  text-shadow: 0px 0px 4px #0085FF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  color: white;
}

.backHome-button {
  position: absolute;
  top: 25px;
  left: 60px;
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  text-shadow: 0px 0px 4px #0085FF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  color: white;
  
  img {
    width: 18px;
    height: 18px;
  }
}

// 缩略图样式
.thumbnail {
  width: 100%;
  height: 160px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  // overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  position: relative;
  margin-bottom: 5px;
  
  &:hover {
    border-color: rgba(0, 89, 255, 0.6);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 89, 255, 0.3);
  }
  
  &.active {
    border-color: #0080ff;
    box-shadow: 0 0 15px rgba(0, 47, 255, 0.6);
    transform: scale(1.05);
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
    
    &:hover {
      opacity: 0.9;
    }
  }
  
  // 视频缩略图容器
  .video-thumbnail-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    
    .video-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.3s ease;
      
      &:hover {
        opacity: 0.9;
      }
    }
    
    .video-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60px;
      height: 60px;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      transition: all 0.3s ease;
      
      svg {
        width: 32px;
        height: 32px;
        margin-left: 4px;
      }
      
      &:hover {
        background: rgba(0, 89, 255, 0.8);
        transform: translate(-50%, -50%) scale(1.1);
      }
    }
  }
}
</style>

