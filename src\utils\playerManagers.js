import tokenManager from './tokenManager';
import {
  safeDestroyEZUIKit,
  safeInitEZUIKit,
  setupEZUIKitVisibilityHandler,
  cleanupEZUIKitResources
} from './ezuikitFix';

/**
 * 播放器管理器
 * 负责管理监控播放器和视频播放器的生命周期
 */
class PlayerManager {
  constructor() {
    // 播放器实例映射
    this.players = new Map();
    // 可见性处理器映射
    this.visibilityHandlers = new Map();
    // 加载状态映射
    this.loadingStates = new Map();
    // 错误状态映射
    this.errorStates = new Map();
    // 正在初始化的播放器集合（防重复初始化）
    this.initializingPlayers = new Set();
    // DOM观察者
    this.domObserver = null;
    this.setupDOMObserver();

    // EZUIKit主题配置
    this.ezuikitTheme = {
      autoFocus: 5,
      poster: "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
      header: {
        color: "#1890ff",
        activeColor: "#FFFFFF",
        backgroundColor: "#000000",
        btnList: []
      },
      footer: {
        color: "#FFFFFF",
        activeColor: "#1890FF",
        backgroundColor: "#00000051",
        btnList: [
          {
            iconId: "play",
            part: "left",
            defaultActive: 1,
            memo: "播放",
            isrender: 1,
          },
          {
            iconId: "capturePicture",
            part: "left",
            defaultActive: 0,
            memo: "截屏按钮",
            isrender: 1,
          },
          {
            iconId: "sound",
            part: "left",
            defaultActive: 0,
            memo: "声音按钮",
            isrender: 1,
          },
          {
            iconId: "recordvideo",
            part: "left",
            defaultActive: 0,
            memo: "录制按钮",
            isrender: 1,
          },
          {
            iconId: "expend",
            part: "right",
            defaultActive: 0,
            memo: "全局全屏按钮",
            isrender: 1,
          },
        ],
      },
    };
  }

  /**
   * 设置DOM变化观察者，用于检测播放器容器是否从DOM中移除
   */
  setupDOMObserver() {
    this.domObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.removedNodes.length > 0) {
          // 优化：只在有节点被移除时检查
          this.checkAndRestorePlayers();
        }
      });
    });
  }

  /**
   * 检查播放器容器状态，并在需要时恢复
   */
  checkAndRestorePlayers() {
    for (const [playerId, playerInfo] of this.players.entries()) {
      const container = document.getElementById(playerId);
      if (!container && playerInfo.instance) {
        // 容器被移除，但实例仍在，标记为待恢复
        console.log(`[DOM Observer] 检测到播放器容器 ${playerId} 已被移除，准备恢复...`);
        // 可以在这里添加一个标记，而不是立即恢复
        playerInfo.needsRestore = true;
      } else if (container && playerInfo.needsRestore) {
        // 容器重新出现，执行恢复
        console.log(`[DOM Observer] 检测到播放器容器 ${playerId} 已重新添加，开始恢复...`);
        this.restorePlayer(playerId, playerInfo);
        delete playerInfo.needsRestore;
      }
    }
  }

  /**
   * 恢复单个播放器
   * @param {string} playerId 
   * @param {Object} playerInfo 
   */
  async restorePlayer(playerId, playerInfo) {
    console.log(`🔧 正在恢复播放器: ${playerId}`);
    try {
      // 销毁旧的实例，但不从map中移除记录
      await this.destroyPlayer(playerId, { keepRecord: true });

      const container = document.getElementById(playerId);
      if (!container) {
        console.warn(`恢复播放器 ${playerId} 失败：容器未找到。`);
        return;
      }
      container.innerHTML = ''; // 清理容器

      // 根据类型重新创建
      if (playerInfo.type === 'monitor' || playerInfo.type === 'major-domestic') {
        console.log(`重新创建监控播放器: ${playerId}`);
        // 确保原始配置信息被传递
        await this.initPlayer({ data: playerInfo.data, type: 'monitor', onLoadingChange: playerInfo.onLoadingChange, onError: playerInfo.onError });
      } else if (playerInfo.type === 'international') {
        console.log(`重新创建项目视频播放器: ${playerId}`);
        await this.initPlayer({ data: playerInfo.data, type: 'international', onLoadingChange: playerInfo.onLoadingChange, onError: playerInfo.onError });
      } else if (playerInfo.type === 'major') {
        console.log(`重新创建重大项目视频播放器: ${playerId}`);
        await this.initPlayer({ data: playerInfo.data, type: 'major', onLoadingChange: playerInfo.onLoadingChange, onError: playerInfo.onError });
      }
    } catch (error) {
      console.error(`恢复播放器 ${playerId} 失败:`, error);
      // 可以在这里设置错误状态
      this.setErrorState(playerId, error.message);
      if (playerInfo.onError) playerInfo.onError(playerId, error);
    }
  }

  /**
   * 初始化监控播放器
   * @param {Object} data - 监控设备数据
   * @param {string} playerId - 播放器ID
   * @param {HTMLElement} playerContainer - 播放器容器
   * @param {Function} onLoadingChange - 加载状态回调
   * @param {Function} onError - 错误回调
   */
  async initCameraPlayer(data, playerId, type, playerContainer, onLoadingChange, onError) {
    try {
      const token = await tokenManager.getHikToken();
      const deviceSerial = data.deviceSerial;
      const channelNo = data.channelNo || '1';
      const videoUrl = `ezopen://open.ys7.com/${deviceSerial}/${channelNo}.live`;

      if (typeof EZUIKit === 'undefined') {
        throw new Error('EZUIKit库未加载');
      }

      // 获取容器实际宽高，用于动态设置播放器尺寸
      const { width: containerWidth, height: containerHeight } = playerContainer.getBoundingClientRect();

      const playerConfig = {
        staticPath: "/ezuikit_static/v65",
        id: playerId,
        accessToken: token,
        url: videoUrl,
        quality: 6,
        audio: false,
        themeData: this.ezuikitTheme,
        useHardDev: false,
        width: 1140,
        height: 590,
      };

      const player = await safeInitEZUIKit(playerConfig);
      if (player) {
        this.players.set(playerId, {
          instance: player,
          type: type,
          data,
          onLoadingChange,
          onError
        });

        const visibilityCleanup = setupEZUIKitVisibilityHandler ? setupEZUIKitVisibilityHandler(player) : null;
        if (visibilityCleanup) {
          this.visibilityHandlers.set(playerId, visibilityCleanup);
        }

        this.clearErrorState(playerId);

        if (playerContainer) {
          playerContainer.style.transform = 'translateZ(0)';
          requestAnimationFrame(() => {
            playerContainer.style.transform = '';
          });
        }
      }
    } catch (error) {
      throw error;
    }
  }

  async initVideoPlayer(data, playerId, type, playerContainer, onLoadingChange, onError) {

    const token = await tokenManager.getVideoToken();
    const videoIds = data.sp.split(',').filter(id => id.trim());
    const firstVideoId = videoIds[0];
    const userId = "941981453197164545";
    const videoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${firstVideoId}?access_token=${token}&userid=${userId}`;

    const videoElement = document.createElement('video');
    videoElement.src = videoUrl;
    videoElement.autoplay = true;
    videoElement.loop = true;
    videoElement.muted = true;
    videoElement.className = 'grid-video-player';
    videoElement.style.width = '100%';
    videoElement.style.height = '100%';
    videoElement.style.objectFit = 'cover';
    videoElement.setAttribute('playsinline', '');

    const onCanPlay = () => {
      this.setLoadingState(playerId, false);
      this.clearErrorState(playerId);
      if (onLoadingChange) onLoadingChange(playerId, false);
      videoElement.removeEventListener('canplay', onCanPlay);
    };

    const onErrorHandler = (e) => {
      const errorMsg = e.target.error?.message || '未知错误';
      this.setErrorState(playerId, errorMsg);
      if (onError) onError(playerId, new Error(errorMsg));
      this.setLoadingState(playerId, false);
      if (onLoadingChange) onLoadingChange(playerId, false);
      videoElement.removeEventListener('error', onErrorHandler);
    };

    videoElement.addEventListener('canplay', onCanPlay);
    videoElement.addEventListener('error', onErrorHandler);

    playerContainer.innerHTML = '';
    playerContainer.appendChild(videoElement);

    this.players.set(playerId, {
      instance: videoElement,
      type,
      data,
      onLoadingChange,
      onError
    });

    if (playerContainer) {
      playerContainer.style.transform = 'translateZ(0)';
      requestAnimationFrame(() => {
        playerContainer.style.transform = '';
      });
    }
  }
  /**
   * 通用播放器初始化方法
   * @param {Object} options
   *   - data: 设备/项目数据
   *   - type: 'monitor' | 'international' | 'major'
   *   - onLoadingChange: function
   *   - onError: function
   */
  async initPlayer({ data, type, onLoadingChange, onError }) {
    let playerId = '';
    let playerContainer = null;

    if (type === 'monitor') {
      if (data.channelStatus !== '1') {
        console.log(`设备 ${data.deviceSerial} 通道 ${data.channelNo} 不在线，跳过初始化`);
        return;
      }
      playerId = `monitor-player-${data.deviceSerial}-${data.channelNo}`;
    } else if (type === 'international') {
      if (!data.sp) {
        console.log(`项目 ${data.xmjc} 没有视频，跳过初始化`);
        return;
      }
      playerId = `international-player-${data.id}`;
    } else if (type === 'major') {
      if (!data.sp) {
        console.log(`重大项目 ${data.xmjc || data.name || data.id} 没有视频，跳过初始化`);
        return;
      }
      playerId = `major-player-${data.id}`;
    } else {
      console.warn('未知播放器类型:', type);
      return;
    }

    // 防重复初始化检查
    if (this.initializingPlayers.has(playerId)) {
      console.log(`播放器 ${playerId} 正在初始化中，跳过重复初始化`);
      return;
    }
    if (this.players.has(playerId) && !this.errorStates.has(playerId)) {
      console.log(`播放器 ${playerId} 已存在且正常，跳过初始化`);
      return;
    }

    playerContainer = document.getElementById(playerId);
    if (!playerContainer) {
      console.warn(`播放器容器 ${playerId} 不存在`);
      return;
    }

    this.initializingPlayers.add(playerId);
    if (this.players.has(playerId)) {
      await this.destroyPlayer(playerId);
    }

    try {
      this.setLoadingState(playerId, true);
      this.clearErrorState(playerId);
      if (onLoadingChange) onLoadingChange(playerId, true);

      if (type === 'monitor') {
        await this.initCameraPlayer(data, playerId, type, playerContainer, onLoadingChange, onError);
      } else if (type === 'international' || type === 'major') {
        await this.initVideoPlayer(data, playerId, type, playerContainer, onLoadingChange, onError);
      }

      this.clearErrorState(playerId);
    } catch (error) {
      this.setErrorState(playerId, error.message);
      // debugger
      // console.log(error)
      if (onError) onError(playerId, error);
    } finally {
      this.initializingPlayers.delete(playerId);
      this.setLoadingState(playerId, false);
      if (onLoadingChange) onLoadingChange(playerId, false);
    }
  }

  /**
   * 销毁指定播放器
   * @param {string} playerId - 播放器ID
   * @param {Object} options - { keepRecord: boolean }
   */
  async destroyPlayer(playerId, options = {}) {
    const playerInfo = this.players.get(playerId);
    if (!playerInfo) return;

    console.log(`🔥 准备销毁播放器: ${playerId}`);

    try {
      if (playerInfo.type === 'monitor') {
        await safeDestroyEZUIKit(playerInfo.instance);
      } else if (playerInfo.type === 'video') {
        const videoElement = playerInfo.instance;
        if (videoElement && videoElement.pause) {
          videoElement.pause();
          videoElement.src = '';
          if (videoElement.parentNode) {
            videoElement.parentNode.removeChild(videoElement);
          }
        }
      }

      console.log(`🗑️ 播放器 ${playerId} 已销毁`);
    } catch (error) {
      console.error(`❌ 销毁播放器 ${playerId} 失败:`, error);
    }

    // 清理相关资源
    this.players.delete(playerId);
    this.loadingStates.delete(playerId);
    this.errorStates.delete(playerId);
    this.initializingPlayers.delete(playerId);

    // 清理可见性处理器
    const visibilityHandler = this.visibilityHandlers.get(playerId);
    if (visibilityHandler) {
      try {
        visibilityHandler();
        this.visibilityHandlers.delete(playerId);
      } catch (error) {
        console.error(`❌ 清理可见性处理器 ${playerId} 失败:`, error);
      }
    }

    if (!options.keepRecord) {
      this.players.delete(playerId);
      this.loadingStates.delete(playerId);
      this.errorStates.delete(playerId);
      console.log(`✅ 播放器 ${playerId} 已完全销毁并清理记录`);
    } else {
      console.log(`✅ 播放器 ${playerId} 实例已销毁，但保留记录用于恢复`);
    }
  }

  /**
   * 销毁所有播放器
   * @param {string} [type] - 可选，只销毁指定类型的播放器 ('monitor' 或 'international')
   */
  async destroyAllPlayers(type) {
    const playersToDestroy = [];
    for (const [playerId, playerInfo] of this.players.entries()) {
      if (!type || playerInfo.type === type) {
        playersToDestroy.push(playerId);
      }
    }

    if (playersToDestroy.length > 0) {
      console.log(`[Player Lifecycle] 准备销毁 ${playersToDestroy.length} 个播放器 (类型: ${type || 'all'})`);
      const destroyPromises = playersToDestroy.map(id => this.destroyPlayer(id));
      await Promise.all(destroyPromises);
      console.log(`[Player Lifecycle] 所有指定的播放器已销毁`);
    }
  }

  /**
   * 获取指定类型的播放器列表
   * @param {string} type - 'monitor' 或 'international'
   * @returns {Array} - 播放器实例数组
   */
  getPlayersByType(type) {
    const players = [];
    for (const [, playerInfo] of this.players.entries()) {
      if (playerInfo.type === type) {
        players.push(playerInfo.instance);
      }
    }
    return players;
  }

  /**
   * 获取指定类型的播放器数量
   * @param {string} type - 'monitor' 或 'international'
   * @returns {number}
   */
  getPlayerCountByType(type) {
    return this.getPlayersByType(type).length;
  }

  /**
   * 按类型暂停播放器
   * @param {string} type - 'monitor' 或 'international'
   */
  pausePlayersByType(type) {
    console.log(`[Player Lifecycle] 暂停类型为 "${type}" 的所有播放器`);
    const playersToPause = [];
    for (const [playerId, playerInfo] of this.players.entries()) {
      if (playerInfo.type === type) {
        playersToPause.push({
          id: playerId,
          instance: playerInfo.instance,
          type: playerInfo.type
        });
      }
    }

    playersToPause.forEach(player => {
      try {
        // 🔥 关键修复: 对 EZUIKit 播放器使用 pause() 而不是 stop()
        // stop() 会彻底断流，导致 play() 可能无法恢复，出现黑屏
        if (player.type === 'monitor' && player.instance && typeof player.instance.pause === 'function') {
          // EZUIKit 播放器使用 pause
          console.log(`Pausing monitor player with pause(): ${player.id}`);
          player.instance.pause();
        } else if (player.type === 'international' && player.instance && typeof player.instance.pause === 'function') {
          // Video 标签播放器使用 pause
          console.log(`Pausing international player: ${player.id}`);
          player.instance.pause();
        }
      } catch (error) {
        console.error(`暂停播放器 ${player.id} 失败:`, error);
      }
    });
  }

  /**
   * 按类型恢复播放器
   * @param {string} type - 'monitor' 或 'international'
   */
  resumePlayersByType(type) {
    console.log(`[Player Lifecycle] 恢复类型为 "${type}" 的所有播放器`);
    const playersToResume = [];
    for (const [playerId, playerInfo] of this.players.entries()) {
      if (playerInfo.type === type) {
        playersToResume.push({
          id: playerId,
          instance: playerInfo.instance,
          type: playerInfo.type
        });
      }
    }

    playersToResume.forEach(player => {
      try {
        if (player.type === 'monitor' && player.instance && typeof player.instance.play === 'function') {
          // EZUIKit 播放器使用 play
          console.log(`Resuming monitor player: ${player.id}`);
          player.instance.play();

          // 强制重绘以解决CSS transform下的渲染黑屏问题
          const playerContainer = document.getElementById(player.id);
          if (playerContainer) {
            console.log(`触发重绘: ${player.id}`);
            playerContainer.style.transform = 'translateZ(0)';
            requestAnimationFrame(() => {
              playerContainer.style.transform = '';
            });
          }
        } else if (player.type === 'international' && player.instance && typeof player.instance.play === 'function') {
          // Video 标签播放器使用 play
          console.log(`Resuming international player: ${player.id}`);
          player.instance.play();
        }
      } catch (error) {
        console.error(`恢复播放器 ${player.id} 失败:`, error);
      }
    });
  }

  /**
   * 暂停所有播放器
   */
  pauseAllPlayers() {
    console.log('⏸️ 暂停所有播放器...');

    this.players.forEach((playerData, playerId) => {
      try {
        if (playerData.type === 'monitor') {
          // EZUIKit播放器暂停
          if (playerData.instance && playerData.instance.pause) {
            playerData.instance.pause();
          }
        } else if (playerData.type === 'video') {
          // HTML5视频播放器暂停
          if (playerData.instance && playerData.instance.pause) {
            playerData.instance.pause();
          }
        }
        console.log(`⏸️ 播放器 ${playerId} 已暂停`);
      } catch (error) {
        console.error(`❌ 暂停播放器 ${playerId} 失败:`, error);
      }
    });
  }

  /**
   * 恢复指定播放器
   * @param {string} playerId - 播放器ID
   */
  resumePlayer(playerId) {
    const playerData = this.players.get(playerId);
    if (!playerData) return;

    try {
      if (playerData.type === 'monitor') {
        if (playerData.instance && playerData.instance.play) {
          playerData.instance.play();
        }
      } else if (playerData.type === 'video') {
        if (playerData.instance && playerData.instance.play) {
          playerData.instance.play();
        }
      }
      console.log(`▶️ 播放器 ${playerId} 已恢复`);
    } catch (error) {
      console.error(`❌ 恢复播放器 ${playerId} 失败:`, error);
    }
  }

  /**
   * 设置加载状态
   * @param {string} playerId - 播放器ID
   * @param {boolean} loading - 是否加载中
   */
  setLoadingState(playerId, loading) {
    this.loadingStates.set(playerId, loading);
  }

  /**
   * 获取加载状态
   * @param {string} playerId - 播放器ID
   * @returns {boolean}
   */
  getLoadingState(playerId) {
    return this.loadingStates.get(playerId) || false;
  }

  /**
   * 设置错误状态
   * @param {string} playerId - 播放器ID
   * @param {string} error - 错误信息
   */
  setErrorState(playerId, error) {
    this.errorStates.set(playerId, error);
  }

  /**
   * 获取错误状态
   * @param {string} playerId - 播放器ID
   * @returns {string|null}
   */
  getErrorState(playerId) {
    return this.errorStates.get(playerId) || null;
  }

  /**
   * 清除错误状态
   * @param {string} playerId - 播放器ID
   */
  clearErrorState(playerId) {
    this.errorStates.delete(playerId);
  }

  /**
   * 检查播放器是否存在
   * @param {string} playerId - 播放器ID
   * @returns {boolean}
   */
  hasPlayer(playerId) {
    return this.players.has(playerId);
  }

  /**
   * 批量检查播放器是否存在
   * @param {Array} playerIds - 播放器ID数组
   * @returns {Object} 包含存在和不存在的播放器ID
   */
  checkPlayersExistence(playerIds) {
    const existing = [];
    const missing = [];

    playerIds.forEach(playerId => {
      if (this.players.has(playerId)) {
        existing.push(playerId);
      } else {
        missing.push(playerId);
      }
    });

    return { existing, missing };
  }

  /**
   * 获取播放器统计信息
   */
  getStats() {
    return {
      totalPlayers: this.players.size,
      monitorPlayers: Array.from(this.players.values()).filter(p => p.type === 'monitor').length,
      videoPlayers: Array.from(this.players.values()).filter(p => p.type === 'video').length,
      loadingPlayers: this.loadingStates.size,
      errorPlayers: this.errorStates.size,
      initializingPlayers: this.initializingPlayers.size
    };
  }

  /**
   * 获取播放器信息
   * @param {string} playerId 
   * @returns {Object|undefined}
   */
  getPlayer(playerId) {
    return this.players.get(playerId);
  }
}

// 创建单例实例
const playerManager = new PlayerManager();

export default playerManager; 