<script>
  import { refreshScale } from "@/assets/js/flexible-dashboard";
  import { watch } from "vue";
  import { useRoute } from "vue-router";
  
  export default {
    name: "appRef",
    setup() {
      const route = useRoute();
      
      // 定义使用windowScale.js的路由，这些路由下不使用App级别的缩放
      const windowScaleRoutes = [
        '/business-display',
        '/video-display'
      ];
      
      // 检查当前路由是否需要跳过App级别缩放
      const shouldSkipAppScaling = () => {
        return windowScaleRoutes.some(routePath => {
          return route.path.startsWith(routePath);
        });
      };
      
      // 缩放处理函数
      const handleScaling = () => {
        if (!shouldSkipAppScaling()) {
          console.log('App.vue: 执行flexible-dashboard缩放');
          refreshScale();
        } else {
          console.log('App.vue: 跳过App级别缩放，使用windowScale.js');
          // 重置App的样式，避免残留的缩放效果
          const appElement = document.getElementById("app");
          if (appElement) {
            // appElement.style.transform = "translate(-50%, -50%)";
            appElement.style.transform = "";
            appElement.style.width = "";
            appElement.style.height = "";
            // appElement.style.top = "50%";
            appElement.style.left = "";
          }
        }
      };
      
      // 监听路由变化
      watch(() => route.path, () => {
        handleScaling();
      }, { immediate: true });
      
      return {
        handleScaling
      };
    },
    mounted() {
      this.handleScaling();
      window.addEventListener("resize", () => {
        this.handleScaling();
      });
    },
  };
</script>

<template>
  <router-view v-slot="{ Component }">
    <keep-alive>
      <component v-if="$route.meta.keepAlive" :is="Component" />
    </keep-alive>
    <component v-if="!$route.meta.keepAlive" :is="Component" />
  </router-view>
</template>

<style scoped></style>
