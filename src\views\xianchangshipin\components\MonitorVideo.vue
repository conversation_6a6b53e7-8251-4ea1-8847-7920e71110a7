<template>
  <div ref="monitorVideoGrid" class="video-grid">
  <div class="video-row" v-for="(row, rowIndex) in displayedMonitorRows" :key="rowIndex">
    <div class="video-item" v-for="(monitor, colIndex) in row"
      :key="monitor.id || `${rowIndex}-${colIndex}`"
      :data-monitor-id="monitor.isEmpty ? undefined : `${monitor.deviceSerial}-${monitor.channelNo}`">
      <div v-if="!monitor.isEmpty" class="monitor-bg">
        <div class="monitor-name" :title="monitor.storeName">
          {{ monitor.storeName }}
        </div>
        <!-- EZUIKit监控播放器 -->
        <div v-if="monitor.channelStatus === '1'" class="monitor-player-wrapper">
          <!-- 加载状态 -->
          <div
            v-if="playerLoadingStates.get(getPlayerId(monitor))"
            class="player-loading-overlay">
            <div class="loading-spinner-small"></div>
            <div class="loading-text-small">正在连接监控...</div>
          </div>
          <!-- 错误状态 -->
          <div
            v-else-if="playerErrorStates.get(getPlayerId(monitor))"
            class="player-error-overlay">
            <div class="error-icon">⚠️</div>
            <div class="error-text-small">连接失败</div>
          </div>
          <!-- 播放器容器 -->
          <div :id="getPlayerId(monitor)" class="monitor-player"></div>
        </div>
        <!-- 离线监控显示 -->
        <div v-else class="offline-monitor">
          <div class="offline-icon">📷</div>
          <div class="offline-text">设备{{ getStatusText(monitor.channelStatus) }}</div>
        </div>
      </div>
      <div v-else class="monitor-bg empty-monitor">
        <div class="monitor-name">等待监控</div>
        <div class="empty-monitor-placeholder">
          <div class="placeholder-icon">📷</div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="monitorData.length === 0 && !loading" class="empty-state">
    <div class="empty-text">暂无监控设备</div>
  </div>
</div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue';
import playerManager from '@/utils/playerManager.js';

const props = defineProps({
  monitorData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  itemsPerRow: {
    type: Number,
    default: 4
  }
});

const emit = defineEmits(['fullscreen']);

const playerLoadingStates = ref(new Map());
const playerErrorStates = ref(new Map());
const observer = ref(null);
const monitorVideoGrid = ref(null);

const displayedMonitorRows = computed(() => {
  const rows = [];
  const itemsPerRow = props.itemsPerRow;
  const monitors = props.monitorData;
  if (!monitors || monitors.length === 0) {
    return [];
  }
  for (let i = 0; i < monitors.length; i += itemsPerRow) {
    const row = monitors.slice(i, i + itemsPerRow);
    rows.push(row);
  }
  const lastRow = rows[rows.length - 1];
  if (lastRow && lastRow.length < itemsPerRow) {
    const placeholdersToAdd = itemsPerRow - lastRow.length;
    for (let i = 0; i < placeholdersToAdd; i++) {
      lastRow.push({
        id: `empty-monitor-${rows.length - 1}-${lastRow.length}`,
        isEmpty: true,
      });
    }
  }
  return rows;
});

function getPlayerId(monitor) {
  return `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
}

function getStatusText(status) {
  switch (status) {
    case '1': return '在线';
    case '0': return '离线';
    case '-1': return '异常';
    default: return '未知';
  }
}

const setupMonitorPlayerObserver = () => {
  console.log('setupMonitorPlayerObserver');
  if (observer.value) observer.value.disconnect();
  const options = {
    root: monitorVideoGrid.value,
    rootMargin: '50px 0px 100px 0px',
    threshold: 0.3,
  };
  observer.value = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const monitorId = target.dataset.monitorId;
        if (!monitorId) return;
        const monitor = props.monitorData.find(
          (m) => !m.isEmpty && `${m.deviceSerial}-${m.channelNo}` === monitorId
        );
        if (monitor) {
          const playerId = getPlayerId(monitor);
          if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
            initializeMonitorPlayer(monitor);
          }
        }
        observer.value.unobserve(target);
      }
    });
  }, options);
  if (monitorVideoGrid.value) {
    const elementsToObserve = monitorVideoGrid.value.querySelectorAll('.video-item[data-monitor-id]');
    elementsToObserve.forEach((el) => {
      observer.value.observe(el);
    });
  }
};

const initializeMonitorPlayer = async (monitor) => {
  
  const playerId = getPlayerId(monitor);
  await playerManager.initPlayer({
    data: monitor,
    type: 'monitor',
    onLoadingChange: (playerId, isLoading) => {
      console.log('onLoadingChange', playerId, isLoading);
      if (isLoading) {
        playerLoadingStates.value.set(playerId, true);
      } else {
        playerLoadingStates.value.delete(playerId);
      }
    },
    onError: (playerId, error) => {
      playerErrorStates.value.set(playerId, error.message);
      playerLoadingStates.value.delete(playerId);
    }
  });
};

onMounted(() => {
  nextTick(() => {
    setupMonitorPlayerObserver();
  });
});

onBeforeUnmount(() => {
  if (observer.value) observer.value.disconnect();
  playerManager.destroyAllPlayers('monitor');
});

watch(() => props.monitorData, async () => {
  await nextTick();
  setupMonitorPlayerObserver();
}, { deep: true });
</script>

<style lang="scss" scoped>
.video-row {
  min-height: 120px;
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}
.video-item {
  flex: 1;
  aspect-ratio: 16/9;
  min-height: 140px;
  border-radius: 8px;
  overflow: hidden;
}


.monitor-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  transition: all 0.3s ease;
}

.monitor-bg:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.monitor-name {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  height: auto;
  padding: 8px 12px;
  box-sizing: border-box;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 12px;
  color: #00FFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.monitor-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.monitor-player-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
// 空状态样式
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
}
.error-text-small {
  color: #FF6B6B;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}
.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

.player-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}

.player-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 44, 52, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.loading-text-small {
  color: #00FFFF;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}
.empty-text {
  font-size: 18px;
  font-weight: 500;
}

.error-text-small {
  color: #FF6B6B;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.error-icon {
  font-size: 20px;
  opacity: 0.8;
  margin-bottom: 2px;
}

.offline-monitor {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.offline-icon {
  font-size: 24px;
  opacity: 0.5;
  margin-bottom: 4px;
}

.offline-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  text-align: center;
}

.empty-monitor {
  opacity: 0.3;
  pointer-events: none;
}

.empty-monitor-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.placeholder-icon {
  font-size: 30px;
  opacity: 0.5;
}
.video-grid {
  width: 100%;
  height: calc(100% - 80px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}
</style>